import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const isAuth = !!token
    const isAuthPage = req.nextUrl.pathname.startsWith('/auth')
    const isAdminPage = req.nextUrl.pathname.startsWith('/admin')
    const isReporterPage = req.nextUrl.pathname.startsWith('/reporter')
    const isApiRoute = req.nextUrl.pathname.startsWith('/api')

    // Allow public API routes
    if (isApiRoute && req.nextUrl.pathname.startsWith('/api/public')) {
      return NextResponse.next()
    }

    // Allow auth API routes
    if (isApiRoute && req.nextUrl.pathname.startsWith('/api/auth')) {
      return NextResponse.next()
    }

    // Redirect to signin if not authenticated and trying to access protected routes
    if (!isAuth && (isAdminPage || isReporterPage)) {
      return NextResponse.redirect(new URL('/auth/signin', req.url))
    }

    // Redirect authenticated users away from auth pages
    if (isAuth && isAuthPage) {
      const role = token?.role
      if (role === 'ADMIN') {
        return NextResponse.redirect(new URL('/admin', req.url))
      }
      return NextResponse.redirect(new URL('/reporter', req.url))
    }

    // Check admin access
    if (isAdminPage && token?.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/reporter', req.url))
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: () => true, // Let middleware handle the logic
    },
  }
)

export const config = {
  matcher: [
    '/admin/:path*',
    '/reporter/:path*',
    '/auth/:path*',
    '/api/admin/:path*',
    '/api/reporter/:path*'
  ]
}

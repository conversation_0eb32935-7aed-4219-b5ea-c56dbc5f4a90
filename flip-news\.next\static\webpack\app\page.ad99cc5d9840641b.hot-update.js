"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/news-grid.tsx":
/*!**********************************!*\
  !*** ./components/news-grid.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewsGrid: () => (/* binding */ NewsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _news_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./news-card */ \"(app-pages-browser)/./components/news-card.tsx\");\n/* harmony import */ var _ad_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ad-card */ \"(app-pages-browser)/./components/ad-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ NewsGrid auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewsGrid() {\n    _s();\n    const [news, setNews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewsGrid.useEffect\": ()=>{\n            fetchNews();\n            fetchAds();\n        }\n    }[\"NewsGrid.useEffect\"], []);\n    const fetchNews = async ()=>{\n        try {\n            const response = await fetch(\"/api/public/news\");\n            if (response.ok) {\n                const data = await response.json();\n                setNews(data.news || []);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch news:\", error);\n            // For demo purposes, show sample data\n            setNews(sampleNews);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAds = async ()=>{\n        try {\n            const response = await fetch(\"/api/public/ads\");\n            if (response.ok) {\n                const data = await response.json();\n                setAds(data.ads || []);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch ads:\", error);\n            // For demo purposes, show sample ads\n            setAds(sampleAds);\n        }\n    };\n    // Sample data for demo\n    const sampleNews = [\n        {\n            id: \"1\",\n            title: \"Breaking: Major Technology Breakthrough Announced\",\n            excerpt: \"Scientists have made a significant discovery that could revolutionize the tech industry...\",\n            image: \"https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop\",\n            category: \"Technology\",\n            createdAt: new Date().toISOString(),\n            views: 1250,\n            likes: 89,\n            shares: 23,\n            author: {\n                name: \"Tech Reporter\"\n            }\n        },\n        {\n            id: \"2\",\n            title: \"Sports Update: Championship Finals This Weekend\",\n            excerpt: \"The most anticipated match of the season is set to take place this weekend...\",\n            image: \"https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=400&h=300&fit=crop\",\n            category: \"Sports\",\n            createdAt: new Date(Date.now() - 3600000).toISOString(),\n            views: 890,\n            likes: 67,\n            shares: 15,\n            author: {\n                name: \"Sports Desk\"\n            }\n        },\n        {\n            id: \"3\",\n            title: \"Economic Markets Show Strong Growth\",\n            excerpt: \"Financial analysts report positive trends in the global markets this quarter...\",\n            image: \"https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400&h=300&fit=crop\",\n            category: \"Business\",\n            createdAt: new Date(Date.now() - 7200000).toISOString(),\n            views: 654,\n            likes: 45,\n            shares: 12,\n            author: {\n                name: \"Business Reporter\"\n            }\n        }\n    ];\n    const sampleAds = [\n        {\n            id: \"1\",\n            title: \"Special Offer - 50% Off\",\n            image: \"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=200&fit=crop\",\n            link: \"#\"\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n            children: [\n                ...Array(6)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-300 rounded-lg h-48 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-300 rounded mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-300 rounded w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    // Insert ads every 3 news items\n    const contentItems = [];\n    let adIndex = 0;\n    for(let i = 0; i < news.length; i++){\n        contentItems.push({\n            type: \"news\",\n            data: news[i]\n        });\n        // Insert ad every 3 items\n        if ((i + 1) % 3 === 0 && adIndex < ads.length) {\n            contentItems.push({\n                type: \"ad\",\n                data: ads[adIndex]\n            });\n            adIndex++;\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: contentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.type === \"news\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_news_card__WEBPACK_IMPORTED_MODULE_2__.NewsCard, {\n                    news: item.data\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ad_card__WEBPACK_IMPORTED_MODULE_3__.AdCard, {\n                    ad: item.data\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 13\n                }, this)\n            }, \"\".concat(item.type, \"-\").concat(index), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsGrid, \"D7QK5GZWIMvlr+9isYSn2xu/8EA=\");\n_c = NewsGrid;\nvar _c;\n$RefreshReg$(_c, \"NewsGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/news-grid.tsx\n"));

/***/ })

});
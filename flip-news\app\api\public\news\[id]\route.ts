import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const news = await prisma.news.findUnique({
      where: {
        id: params.id,
        status: "PUBLISHED"
      },
      include: {
        author: {
          select: {
            name: true,
            avatar: true
          }
        }
      }
    })

    if (!news) {
      return NextResponse.json(
        { error: "News not found" },
        { status: 404 }
      )
    }

    // Increment view count
    await prisma.news.update({
      where: { id: params.id },
      data: {
        views: {
          increment: 1
        }
      }
    })

    return NextResponse.json({ news })
  } catch (error) {
    console.error("Failed to fetch news:", error)
    return NextResponse.json(
      { error: "Failed to fetch news" },
      { status: 500 }
    )
  }
}

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/flipnews?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# App Configuration
APP_NAME="Flip News"
APP_URL="http://localhost:3000"

# File Upload (Optional - for production)
# UPLOADTHING_SECRET=""
# UPLOADTHING_APP_ID=""

# Or use Cloudinary
# CLOUDINARY_CLOUD_NAME=""
# CLOUDINARY_API_KEY=""
# CLOUDINARY_API_SECRET=""

# Push Notifications (Optional)
# FIREBASE_SERVER_KEY=""
# ONESIGNAL_APP_ID=""
# ONESIGNAL_REST_API_KEY=""

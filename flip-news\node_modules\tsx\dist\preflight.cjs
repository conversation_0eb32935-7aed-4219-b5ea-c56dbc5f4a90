"use strict";var u=Object.defineProperty;var t=(e,n)=>u(e,"name",{value:n,configurable:!0});var a=require("./get-pipe-path-BoR10qr8.cjs"),l=require("node:os"),f=require("node:worker_threads"),p=require("./client-D6NvIMSC.cjs");require("./suppress-warnings.cjs"),require("module"),require("node:path"),require("./temporary-directory-B83uKxJF.cjs"),require("node:net");const d=t((e,n)=>{for(const r of e)process.on(r,s=>{n(s),process.listenerCount(r)===0&&process.exit(128+l.constants.signals[r])});const{listenerCount:i,listeners:o}=process;process.listenerCount=function(r){let s=Reflect.apply(i,this,arguments);return e.includes(r)&&(s-=1),s},process.listeners=function(r){const s=Reflect.apply(o,this,arguments);return e.includes(r)?s.filter(c=>c!==n):s}},"bindHiddenSignalsHandler");f.isMainThread&&(a.require("./cjs/index.cjs"),(async()=>{const e=await p.connectingToServer;e&&d(["SIGINT","SIGTERM"],n=>{e({type:"signal",signal:n})})})());

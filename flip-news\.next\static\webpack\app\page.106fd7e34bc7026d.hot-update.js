"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/news-card.tsx":
/*!**********************************!*\
  !*** ./components/news-card.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewsCard: () => (/* binding */ NewsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _share_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./share-button */ \"(app-pages-browser)/./components/share-button.tsx\");\n/* __next_internal_client_entry_do_not_use__ NewsCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NewsCard(param) {\n    let { news } = param;\n    _s();\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likes, setLikes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(news.likes);\n    const handleLike = async ()=>{\n        try {\n            const response = await fetch(\"/api/news/\".concat(news.id, \"/like\"), {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                setIsLiked(!isLiked);\n                setLikes((prev)=>isLiked ? prev - 1 : prev + 1);\n            }\n        } catch (error) {\n            console.error(\"Failed to like news:\", error);\n            // Optimistic update for demo\n            setIsLiked(!isLiked);\n            setLikes((prev)=>isLiked ? prev - 1 : prev + 1);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return \"Just now\";\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        return date.toLocaleDateString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group relative w-full cursor-pointer perspective-1000\",\n        onClick: ()=>setIsFlipped(!isFlipped),\n        id: \"news-card-\".concat(news.id),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full transition-transform duration-700 transform-style-preserve-3d \".concat(isFlipped ? \"rotate-y-180\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 w-full backface-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-gradient-to-r from-red-500 to-red-600 px-4 py-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1 left-0 right-0 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                ...Array(8)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-300 text-xs\",\n                                                    children: \"⭐\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: news.author.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-lg font-bold\",\n                                                children: \"Flip News\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-48 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: news.image,\n                                    alt: news.title,\n                                    fill: true,\n                                    className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gradient-to-b from-white to-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-gray-900 mb-3 leading-tight\",\n                                        children: news.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 text-sm leading-relaxed mb-4 line-clamp-4\",\n                                        children: news.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 bottom-0 w-20 h-20 opacity-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full rounded-full border-4 border-gray-300 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-xs font-bold\",\n                                                        children: \"FLIP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_share_button__WEBPACK_IMPORTED_MODULE_4__.ShareButton, {\n                                                    news: news,\n                                                    onShare: (e)=>e.stopPropagation()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 w-full h-full backface-hidden rotate-y-180\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-red-500 to-red-700 rounded-lg shadow-lg h-full flex flex-col text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-red-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm opacity-90\",\n                                            children: \"Full Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold\",\n                                            children: \"Flip News\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 flex flex-col justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold mb-4 text-center leading-tight\",\n                                        children: news.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-center mb-6 opacity-90 leading-relaxed\",\n                                        children: news.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-around mb-6 text-sm bg-red-600 bg-opacity-50 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5 mb-1 \".concat(isLiked ? \"fill-current text-red-200\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: likes\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: news.views\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: news.shares\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleLike();\n                                                },\n                                                className: \"flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors \".concat(isLiked ? \"bg-red-200 text-red-800\" : \"bg-white text-red-600 hover:bg-red-50\"),\n                                                children: isLiked ? \"Liked\" : \"Like\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/news/\".concat(news.id),\n                                                className: \"flex-1 bg-white text-red-600 py-2 px-4 rounded-full text-sm font-medium hover:bg-red-50 transition-colors text-center\",\n                                                onClick: (e)=>e.stopPropagation(),\n                                                children: \"Read More\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-red-400 text-center text-sm opacity-75\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formatDate(news.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"By \",\n                                                news.author.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsCard, \"0KF6zs3jxVXe7PaRoExOy21BoJk=\");\n_c = NewsCard;\nvar _c;\n$RefreshReg$(_c, \"NewsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/news-card.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/eye.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Eye)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0\",\n            key: \"1nclc0\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n];\nconst Eye = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"eye\", __iconNode);\n //# sourceMappingURL=eye.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/heart.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Heart)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\",\n            key: \"c3ymky\"\n        }\n    ]\n];\nconst Heart = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"heart\", __iconNode);\n //# sourceMappingURL=heart.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\n"));

/***/ })

});
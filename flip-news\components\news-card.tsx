"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Heart, Share2, <PERSON>, Clock } from "lucide-react"
import { ShareButton } from "./share-button"

interface NewsCardProps {
  news: {
    id: string
    title: string
    excerpt: string
    image: string
    category: string
    createdAt: string
    views: number
    likes: number
    shares: number
    author: {
      name: string
    }
  }
}

export function NewsCard({ news }: NewsCardProps) {
  const [isFlipped, setIsFlipped] = useState(false)
  const [isLiked, setIsLiked] = useState(false)
  const [likes, setLikes] = useState(news.likes)

  const handleLike = async () => {
    try {
      const response = await fetch(`/api/news/${news.id}/like`, {
        method: "POST"
      })

      if (response.ok) {
        setIsLiked(!isLiked)
        setLikes(prev => isLiked ? prev - 1 : prev + 1)
      }
    } catch (error) {
      console.error("Failed to like news:", error)
      // Optimistic update for demo
      setIsLiked(!isLiked)
      setLikes(prev => isLiked ? prev - 1 : prev + 1)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    return date.toLocaleDateString()
  }

  return (
    <div
      className="group relative w-full cursor-pointer perspective-1000"
      onClick={() => setIsFlipped(!isFlipped)}
      id={`news-card-${news.id}`}
    >
      <div className={`relative w-full transition-transform duration-700 transform-style-preserve-3d ${
        isFlipped ? "rotate-y-180" : ""
      }`}>
        {/* Front Side - Telugu News Card Style */}
        <div className="absolute inset-0 w-full backface-hidden">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            {/* Red Header with decorative elements */}
            <div className="relative bg-gradient-to-r from-red-500 to-red-600 px-4 py-3">
              {/* Decorative stars */}
              <div className="absolute top-1 left-0 right-0 flex justify-center">
                <div className="flex space-x-1">
                  {[...Array(8)].map((_, i) => (
                    <span key={i} className="text-yellow-300 text-xs">⭐</span>
                  ))}
                </div>
              </div>

              {/* Header content */}
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center space-x-3">
                  <span className="text-white text-sm font-medium">
                    {news.author.name}
                  </span>
                </div>
                <div className="text-white text-lg font-bold">
                  Flip News
                </div>
              </div>
            </div>

            {/* Main Image */}
            <div className="relative h-48 overflow-hidden">
              <Image
                src={news.image}
                alt={news.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>

            {/* Telugu-style content */}
            <div className="p-4 bg-gradient-to-b from-white to-gray-50">
              {/* Telugu title */}
              <h3 className="text-lg font-bold text-gray-900 mb-3 leading-tight">
                {news.title}
              </h3>

              {/* Telugu content */}
              <p className="text-gray-700 text-sm leading-relaxed mb-4 line-clamp-4">
                {news.excerpt}
              </p>

              {/* Bottom section with watermark and share */}
              <div className="relative">
                {/* Watermark circle */}
                <div className="absolute right-0 bottom-0 w-20 h-20 opacity-10">
                  <div className="w-full h-full rounded-full border-4 border-gray-300 flex items-center justify-center">
                    <span className="text-gray-400 text-xs font-bold">FLIP</span>
                  </div>
                </div>

                {/* Share button */}
                <div className="flex justify-end">
                  <ShareButton
                    news={news}
                    onShare={(e) => e.stopPropagation()}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Back Side - Enhanced Telugu Style */}
        <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180">
          <div className="bg-gradient-to-br from-red-500 to-red-700 rounded-lg shadow-lg h-full flex flex-col text-white">
            {/* Header */}
            <div className="p-4 border-b border-red-400">
              <div className="flex items-center justify-between">
                <span className="text-sm opacity-90">Full Story</span>
                <span className="text-lg font-bold">Flip News</span>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 p-4 flex flex-col justify-center">
              <h3 className="text-lg font-bold mb-4 text-center leading-tight">
                {news.title}
              </h3>
              <p className="text-sm text-center mb-6 opacity-90 leading-relaxed">
                {news.excerpt}
              </p>

              {/* Stats */}
              <div className="flex items-center justify-around mb-6 text-sm bg-red-600 bg-opacity-50 rounded-lg p-3">
                <div className="flex flex-col items-center">
                  <Heart className={`w-5 h-5 mb-1 ${isLiked ? "fill-current text-red-200" : ""}`} />
                  <span>{likes}</span>
                </div>
                <div className="flex flex-col items-center">
                  <Eye className="w-5 h-5 mb-1" />
                  <span>{news.views}</span>
                </div>
                <div className="flex flex-col items-center">
                  <Share2 className="w-5 h-5 mb-1" />
                  <span>{news.shares}</span>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleLike()
                  }}
                  className={`flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors ${
                    isLiked
                      ? "bg-red-200 text-red-800"
                      : "bg-white text-red-600 hover:bg-red-50"
                  }`}
                >
                  {isLiked ? "Liked" : "Like"}
                </button>

                <Link
                  href={`/news/${news.id}`}
                  className="flex-1 bg-white text-red-600 py-2 px-4 rounded-full text-sm font-medium hover:bg-red-50 transition-colors text-center"
                  onClick={(e) => e.stopPropagation()}
                >
                  Read More
                </Link>
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-red-400 text-center text-sm opacity-75">
              <div className="flex items-center justify-between">
                <span>{formatDate(news.createdAt)}</span>
                <span>By {news.author.name}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

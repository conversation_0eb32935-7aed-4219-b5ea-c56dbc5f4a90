"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Heart, Share2, <PERSON>, Clock } from "lucide-react"
import { ShareButton } from "./share-button"

interface NewsCardProps {
  news: {
    id: string
    title: string
    excerpt: string
    image: string
    category: string
    createdAt: string
    views: number
    likes: number
    shares: number
    author: {
      name: string
    }
  }
}

export function NewsCard({ news }: NewsCardProps) {
  const [isFlipped, setIsFlipped] = useState(false)
  const [isLiked, setIsLiked] = useState(false)
  const [likes, setLikes] = useState(news.likes)

  const handleLike = async () => {
    try {
      const response = await fetch(`/api/news/${news.id}/like`, {
        method: "POST"
      })
      
      if (response.ok) {
        setIsLiked(!isLiked)
        setLikes(prev => isLiked ? prev - 1 : prev + 1)
      }
    } catch (error) {
      console.error("Failed to like news:", error)
      // Optimistic update for demo
      setIsLiked(!isLiked)
      setLikes(prev => isLiked ? prev - 1 : prev + 1)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    return date.toLocaleDateString()
  }

  return (
    <div 
      className="group relative w-full h-96 cursor-pointer perspective-1000"
      onClick={() => setIsFlipped(!isFlipped)}
      id={`news-card-${news.id}`}
    >
      <div className={`relative w-full h-full transition-transform duration-700 transform-style-preserve-3d ${
        isFlipped ? "rotate-y-180" : ""
      }`}>
        {/* Front Side */}
        <div className="absolute inset-0 w-full h-full backface-hidden">
          <div className="bg-white rounded-lg shadow-md overflow-hidden h-full flex flex-col">
            {/* Image */}
            <div className="relative h-48 overflow-hidden">
              <Image
                src={news.image}
                alt={news.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute top-2 left-2">
                <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                  {news.category}
                </span>
              </div>
            </div>

            {/* Content */}
            <div className="p-4 flex-1 flex flex-col">
              <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 text-sm">
                {news.title}
              </h3>
              <p className="text-gray-600 text-xs mb-3 line-clamp-2 flex-1">
                {news.excerpt}
              </p>

              {/* Meta Info */}
              <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                <span className="flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {formatDate(news.createdAt)}
                </span>
                <span>By {news.author.name}</span>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleLike()
                    }}
                    className={`flex items-center space-x-1 text-xs ${
                      isLiked ? "text-red-500" : "text-gray-500 hover:text-red-500"
                    }`}
                  >
                    <Heart className={`w-4 h-4 ${isLiked ? "fill-current" : ""}`} />
                    <span>{likes}</span>
                  </button>
                  
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <Eye className="w-4 h-4" />
                    <span>{news.views}</span>
                  </div>
                </div>

                <ShareButton 
                  news={news}
                  onShare={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Back Side */}
        <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180">
          <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg shadow-md h-full flex flex-col justify-center items-center text-white p-6">
            <h3 className="text-lg font-bold mb-4 text-center">
              {news.title}
            </h3>
            <p className="text-blue-100 text-sm text-center mb-6 leading-relaxed">
              {news.excerpt}
            </p>
            <Link
              href={`/news/${news.id}`}
              className="bg-white text-blue-600 px-6 py-2 rounded-full font-medium hover:bg-blue-50 transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              Read Full Story
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

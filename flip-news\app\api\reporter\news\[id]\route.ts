import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const news = await prisma.news.findFirst({
      where: {
        id: params.id,
        authorId: session.user.id
      },
      include: {
        author: {
          select: {
            name: true,
            avatar: true
          }
        }
      }
    })

    if (!news) {
      return NextResponse.json(
        { error: "News not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ news })
  } catch (error) {
    console.error("Failed to fetch news:", error)
    return NextResponse.json(
      { error: "Failed to fetch news" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { title, content, excerpt, category, tags, image, status } = await request.json()

    // Check if news exists and belongs to user
    const existingNews = await prisma.news.findFirst({
      where: {
        id: params.id,
        authorId: session.user.id
      }
    })

    if (!existingNews) {
      return NextResponse.json(
        { error: "News not found" },
        { status: 404 }
      )
    }

    const updateData: any = {
      title,
      content,
      excerpt: excerpt || content.substring(0, 300) + "...",
      category,
      tags: tags || [],
      image,
      status: status || existingNews.status,
      updatedAt: new Date()
    }

    // Set publishedAt if publishing for the first time
    if (status === "PUBLISHED" && existingNews.status !== "PUBLISHED") {
      updateData.publishedAt = new Date()
    }

    const news = await prisma.news.update({
      where: { id: params.id },
      data: updateData,
      include: {
        author: {
          select: {
            name: true,
            avatar: true
          }
        }
      }
    })

    return NextResponse.json({ news })
  } catch (error) {
    console.error("Failed to update news:", error)
    return NextResponse.json(
      { error: "Failed to update news" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if news exists and belongs to user
    const existingNews = await prisma.news.findFirst({
      where: {
        id: params.id,
        authorId: session.user.id
      }
    })

    if (!existingNews) {
      return NextResponse.json(
        { error: "News not found" },
        { status: 404 }
      )
    }

    await prisma.news.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to delete news:", error)
    return NextResponse.json(
      { error: "Failed to delete news" },
      { status: 500 }
    )
  }
}

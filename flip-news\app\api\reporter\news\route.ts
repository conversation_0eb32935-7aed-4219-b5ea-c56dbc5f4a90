import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const news = await prisma.news.findMany({
      where: {
        authorId: session.user.id
      },
      orderBy: {
        createdAt: "desc"
      },
      include: {
        author: {
          select: {
            name: true,
            avatar: true
          }
        }
      }
    })

    return NextResponse.json({ news })
  } catch (error) {
    console.error("Failed to fetch reporter news:", error)
    return NextResponse.json(
      { error: "Failed to fetch news" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { title, content, excerpt, category, tags, image, status } = await request.json()

    if (!title || !content || !category) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    const news = await prisma.news.create({
      data: {
        title,
        content,
        excerpt: excerpt || content.substring(0, 300) + "...",
        category,
        tags: tags || [],
        image,
        status: status || "DRAFT",
        authorId: session.user.id,
        publishedAt: status === "PUBLISHED" ? new Date() : null
      },
      include: {
        author: {
          select: {
            name: true,
            avatar: true
          }
        }
      }
    })

    return NextResponse.json({ news })
  } catch (error) {
    console.error("Failed to create news:", error)
    return NextResponse.json(
      { error: "Failed to create news" },
      { status: 500 }
    )
  }
}

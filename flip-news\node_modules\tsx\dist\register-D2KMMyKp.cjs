"use strict";var B=Object.defineProperty;var c=(t,e)=>B(t,"name",{value:e,configurable:!0});var z=require("./get-pipe-path-BoR10qr8.cjs"),p=require("node:module"),m=require("node:path"),O=require("node:url"),x=require("get-tsconfig"),G=require("node:fs"),v=require("./index-CylV0-__.cjs"),U=require("./client-D6NvIMSC.cjs");const F=c(t=>{if(!t.startsWith("data:text/javascript,"))return;const e=t.indexOf("?");if(e===-1)return;const n=new URLSearchParams(t.slice(e+1)).get("filePath");if(n)return n},"getOriginalFilePath"),R=c(t=>{const e=F(t);return e&&(p._cache[e]=p._cache[t],delete p._cache[t],t=e),t},"interopCjsExports"),H=c(t=>{const e=t.indexOf(":");if(e!==-1)return t.slice(0,e)},"getScheme"),W=c(t=>t[0]==="."&&(t[1]==="/"||t[1]==="."||t[2]==="/"),"isRelativePath"),y=c(t=>W(t)||m.isAbsolute(t),"isFilePath"),X=c(t=>{if(y(t))return!0;const e=H(t);return e&&e!=="node"},"requestAcceptsQuery"),E="file://",L=/\.([cm]?ts|[tj]sx)($|\?)/,K=/[/\\].+\.(?:cts|cjs)(?:$|\?)/,V=/\.json($|\?)/,_=/\/(?:$|\?)/,Y=/^(?:@[^/]+\/)?[^/\\]+$/,$=`${m.sep}node_modules${m.sep}`;exports.fileMatcher=void 0,exports.tsconfigPathsMatcher=void 0,exports.allowJs=!1;const D=c(t=>{let e=null;if(t){const r=m.resolve(t);e={path:r,config:x.parseTsconfig(r)}}else{try{e=x.getTsconfig()}catch{}if(!e)return}exports.fileMatcher=x.createFilesMatcher(e),exports.tsconfigPathsMatcher=x.createPathsMatcher(e),exports.allowJs=e?.config.compilerOptions?.allowJs??!1},"loadTsconfig"),M=c(t=>Array.from(t).length>0?`?${t.toString()}`:"","urlSearchParamsStringify"),Z=`
//# sourceMappingURL=data:application/json;base64,`,N=c(()=>process.sourceMapsEnabled??!0,"shouldApplySourceMap"),S=c(({code:t,map:e})=>t+Z+Buffer.from(JSON.stringify(e),"utf8").toString("base64"),"inlineSourceMap"),q=[".cts",".mts",".ts",".tsx",".jsx"],ee=[".js",".cjs",".mjs"],C=[".ts",".tsx",".jsx"],w=c((t,e,r,n)=>{const s=Object.getOwnPropertyDescriptor(t,e);s?.set?t[e]=r:(!s||s.configurable)&&Object.defineProperty(t,e,{value:r,enumerable:s?.enumerable||n?.enumerable,writable:n?.writable??(s?s.writable:!0),configurable:n?.configurable??(s?s.configurable:!0)})},"safeSet"),te=c((t,e,r)=>{const n=e[".js"],s=c((a,i)=>{if(t.enabled===!1)return n(a,i);const[l,f]=i.split("?");if((new URLSearchParams(f).get("namespace")??void 0)!==r)return n(a,i);a.id.startsWith("data:text/javascript,")&&(a.path=m.dirname(l)),U.parent?.send&&U.parent.send({type:"dependency",path:l});const d=q.some(h=>l.endsWith(h)),P=ee.some(h=>l.endsWith(h));if(!d&&!P)return n(a,l);let u=G.readFileSync(l,"utf8");if(l.endsWith(".cjs")){const h=v.transformDynamicImport(i,u);h&&(u=N()?S(h):h.code)}else if(d||v.isESM(u)){const h=v.transformSync(u,i,{tsconfigRaw:exports.fileMatcher?.(l)});u=N()?S(h):h.code}a._compile(u,l)},"transformer");w(e,".js",s);for(const a of C)w(e,a,s,{enumerable:!r,writable:!0,configurable:!0});return w(e,".mjs",s,{writable:!0,configurable:!0}),()=>{e[".js"]===s&&(e[".js"]=n);for(const a of[...C,".mjs"])e[a]===s&&delete e[a]}},"createExtensions"),se=c(t=>e=>{if((e==="."||e===".."||e.endsWith("/.."))&&(e+="/"),_.test(e)){let r=m.join(e,"index.js");e.startsWith("./")&&(r=`./${r}`);try{return t(r)}catch{}}try{return t(e)}catch(r){const n=r;if(n.code==="MODULE_NOT_FOUND")try{return t(`${e}${m.sep}index.js`)}catch{}throw n}},"createImplicitResolver"),A=[".js",".json"],J=[".ts",".tsx",".jsx"],ne=[...J,...A],re=[...A,...J],g=Object.create(null);g[".js"]=[".ts",".tsx",".js",".jsx"],g[".jsx"]=[".tsx",".ts",".jsx",".js"],g[".cjs"]=[".cts"],g[".mjs"]=[".mts"];const Q=c(t=>{const e=t.split("?"),r=e[1]?`?${e[1]}`:"",[n]=e,s=m.extname(n),a=[],i=g[s];if(i){const f=n.slice(0,-s.length);a.push(...i.map(o=>f+o+r))}const l=!(t.startsWith(E)||y(n))||n.includes($)||n.includes("/node_modules/")?re:ne;return a.push(...l.map(f=>n+f+r)),a},"mapTsExtensions"),b=c((t,e,r)=>{if(_.test(e)||!r&&!exports.allowJs)return;const n=Q(e);if(n)for(const s of n)try{return t(s)}catch(a){const{code:i}=a;if(i!=="MODULE_NOT_FOUND"&&i!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw a}},"resolveTsFilename"),ae=c((t,e)=>r=>{if(y(r)){const n=b(t,r,e);if(n)return n}try{return t(r)}catch(n){const s=n;if(s.code==="MODULE_NOT_FOUND"){if(typeof s.path=="string"&&s.path.endsWith(`${m.sep}package.json`)){const i=s.message.match(/^Cannot find module '([^']+)'$/);if(i){const f=i[1],o=b(t,f,e);if(o)return o}const l=s.message.match(/^Cannot find module '([^']+)'. Please verify that the package.json has a valid "main" entry$/);if(l){const f=l[1],o=b(t,f,e);if(o)return o}}const a=b(t,r,e);if(a)return a}throw s}},"createTsExtensionResolver"),I="at cjsPreparseModuleExports (node:internal",ce=c(t=>{const e=t.stack.split(`
`).slice(1);return e[1].includes(I)||e[2].includes(I)},"isFromCjsLexer"),ie=c((t,e)=>{const r=t.split("?"),n=new URLSearchParams(r[1]);if(e?.filename){const s=F(e.filename);let a;if(s){const f=s.split("?"),o=f[0];a=f[1],e.filename=o,e.path=m.dirname(o),e.paths=p._nodeModulePaths(e.path),p._cache[o]=e}a||(a=e.filename.split("?")[1]);const l=new URLSearchParams(a).get("namespace");l&&n.append("namespace",l)}return[r[0],n,(s,a)=>(m.isAbsolute(s)&&!s.endsWith(".json")&&!s.endsWith(".node")&&!(a===0&&ce(new Error))&&(s+=M(n)),s)]},"preserveQuery"),oe=c((t,e,r)=>{if(t.startsWith(E)&&(t=O.fileURLToPath(t)),exports.tsconfigPathsMatcher&&!y(t)&&!e?.filename?.includes($)){const n=exports.tsconfigPathsMatcher(t);for(const s of n)try{return r(s)}catch{}}return r(t)},"resolveTsPaths"),le=c((t,e,r)=>(n,s,...a)=>{if(t.enabled===!1)return e(n,s,...a);n=R(n);const[i,l,f]=ie(n,s);if((l.get("namespace")??void 0)!==r)return e(n,s,...a);let o=c(P=>e(P,s,...a),"nextResolveSimple");o=ae(o,!!(r||s?.filename&&L.test(s.filename))),o=se(o);const d=oe(i,s,o);return f(d,a.length)},"createResolveFilename"),k=c((t,e)=>{if(!e)throw new Error("The current file path (__filename or import.meta.url) must be provided in the second argument of tsx.require()");return t.startsWith(".")?((typeof e=="string"&&e.startsWith(E)||e instanceof URL)&&(e=O.fileURLToPath(e)),m.resolve(m.dirname(e),t)):t},"resolveContext"),fe=c(t=>{const{sourceMapsEnabled:e}=process,r={enabled:!0};D(process.env.TSX_TSCONFIG_PATH),process.setSourceMapsEnabled(!0);const n=p._resolveFilename,s=le(r,n,t?.namespace);p._resolveFilename=s;const a=te(r,p._extensions,t?.namespace),i=c(()=>{e===!1&&process.setSourceMapsEnabled(!1),r.enabled=!1,p._resolveFilename===s&&(p._resolveFilename=n),a()},"unregister");if(t?.namespace){const l=c((o,d)=>{const P=k(o,d),[u,h]=P.split("?"),j=new URLSearchParams(h);return t.namespace&&!u.startsWith("node:")&&j.set("namespace",t.namespace),z.require(u+M(j))},"scopedRequire");i.require=l;const f=c((o,d,P)=>{const u=k(o,d),[h,j]=u.split("?"),T=new URLSearchParams(j);return t.namespace&&!h.startsWith("node:")&&T.set("namespace",t.namespace),s(h+M(T),module,!1,P)},"scopedResolve");i.resolve=f,i.unregister=i}return i},"register");exports.cjsExtensionPattern=K,exports.fileUrlPrefix=E,exports.inlineSourceMap=S,exports.interopCjsExports=R,exports.isBarePackageNamePattern=Y,exports.isDirectoryPattern=_,exports.isJsonPattern=V,exports.isRelativePath=W,exports.loadTsconfig=D,exports.mapTsExtensions=Q,exports.register=fe,exports.requestAcceptsQuery=X,exports.tsExtensionsPattern=L;

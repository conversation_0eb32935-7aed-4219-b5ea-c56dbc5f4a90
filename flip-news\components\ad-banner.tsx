"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { X } from "lucide-react"

interface Ad {
  id: string
  title: string
  image: string
  link: string
}

export function AdBanner() {
  const [ad, setAd] = useState<Ad | null>(null)
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    fetchBannerAd()
  }, [])

  const fetchBannerAd = async () => {
    try {
      const response = await fetch("/api/public/ads/banner")
      if (response.ok) {
        const data = await response.json()
        setAd(data.ad)
      }
    } catch (error) {
      console.error("Failed to fetch banner ad:", error)
      // Sample ad for demo
      setAd({
        id: "banner-1",
        title: "Special Holiday Sale - Up to 70% Off!",
        image: "https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=800&h=200&fit=crop",
        link: "#"
      })
    }
  }

  const handleClick = async () => {
    if (!ad) return

    try {
      // Track ad click
      await fetch(`/api/ads/${ad.id}/click`, {
        method: "POST"
      })
    } catch (error) {
      console.error("Failed to track ad click:", error)
    }

    // Open link
    if (ad.link && ad.link !== "#") {
      window.open(ad.link, "_blank", "noopener,noreferrer")
    }
  }

  const handleClose = () => {
    setIsVisible(false)
  }

  if (!ad || !isVisible) {
    return null
  }

  return (
    <div className="relative mb-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg overflow-hidden border border-blue-200">
      <div 
        className="cursor-pointer"
        onClick={handleClick}
      >
        <div className="relative h-32 md:h-40">
          <Image
            src={ad.image}
            alt={ad.title}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
            <div className="text-center text-white">
              <h3 className="text-lg md:text-xl font-bold mb-2">
                {ad.title}
              </h3>
              <p className="text-sm opacity-90">
                Click to learn more
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Close button */}
      <button
        onClick={handleClose}
        className="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1 hover:bg-opacity-70 transition-colors"
      >
        <X className="w-4 h-4" />
      </button>

      {/* Ad label */}
      <div className="absolute top-2 left-2">
        <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
          Advertisement
        </span>
      </div>
    </div>
  )
}

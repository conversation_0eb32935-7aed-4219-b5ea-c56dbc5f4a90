/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c46e46136136\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFnYXJhanVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVm9pY2ViaXJkIG5ld3NcXGZsaXAtbmV3c1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImM0NmU0NjEzNjEzNlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Flip News - Your Mobile News Experience\",\n    description: \"Stay updated with the latest news in a mobile-first, shareable format\",\n    keywords: \"news, mobile, sharing, flip news, breaking news\",\n    authors: [\n        {\n            name: \"Flip News Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_news_grid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/news-grid */ \"(rsc)/./components/news-grid.tsx\");\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/header */ \"(rsc)/./components/header.tsx\");\n/* harmony import */ var _components_ad_banner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ad-banner */ \"(rsc)/./components/ad-banner.tsx\");\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-red-50 to-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-red-500 to-red-600 text-white py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-4\",\n                                children: [\n                                    ...Array(12)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-300 text-lg mx-1\",\n                                        children: \"⭐\"\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold mb-2\",\n                                children: \"Flip News\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-100 text-lg\",\n                                children: \"తెలుగు వార్తలు • Telugu News • Breaking Updates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"తాజా వార్తలు • Latest News\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"అప్‌డేట్‌లతో ఉండండి • Stay updated with breaking news and trending stories\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ad_banner__WEBPACK_IMPORTED_MODULE_3__.AdBanner, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_news_grid__WEBPACK_IMPORTED_MODULE_1__.NewsGrid, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ad-banner.tsx":
/*!**********************************!*\
  !*** ./components/ad-banner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdBanner: () => (/* binding */ AdBanner)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AdBanner = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AdBanner() from the server but AdBanner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\Voicebird news\\flip-news\\components\\ad-banner.tsx",
"AdBanner",
);

/***/ }),

/***/ "(rsc)/./components/header.tsx":
/*!*******************************!*\
  !*** ./components/header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\Voicebird news\\flip-news\\components\\header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./components/news-grid.tsx":
/*!**********************************!*\
  !*** ./components/news-grid.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NewsGrid: () => (/* binding */ NewsGrid)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NewsGrid = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NewsGrid() from the server but NewsGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\Voicebird news\\flip-news\\components\\news-grid.tsx",
"NewsGrid",
);

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\Voicebird news\\flip-news\\components\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnagaraju%5CDocuments%5Caugment-projects%5CVoicebird%20news%5Cflip-news%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnagaraju%5CDocuments%5Caugment-projects%5CVoicebird%20news%5Cflip-news&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnagaraju%5CDocuments%5Caugment-projects%5CVoicebird%20news%5Cflip-news%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnagaraju%5CDocuments%5Caugment-projects%5CVoicebird%20news%5Cflip-news&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnagaraju%5CDocuments%5Caugment-projects%5CVoicebird%20news%5Cflip-news%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnagaraju%5CDocuments%5Caugment-projects%5CVoicebird%20news%5Cflip-news&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cad-banner.tsx%22%2C%22ids%22%3A%5B%22AdBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cnews-grid.tsx%22%2C%22ids%22%3A%5B%22NewsGrid%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cad-banner.tsx%22%2C%22ids%22%3A%5B%22AdBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cnews-grid.tsx%22%2C%22ids%22%3A%5B%22NewsGrid%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ad-banner.tsx */ \"(rsc)/./components/ad-banner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/header.tsx */ \"(rsc)/./components/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/news-grid.tsx */ \"(rsc)/./components/news-grid.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hZ2FyYWp1JTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1ZvaWNlYmlyZCUyMG5ld3MlNUMlNUNmbGlwLW5ld3MlNUMlNUNjb21wb25lbnRzJTVDJTVDYWQtYmFubmVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkFkQmFubmVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hZ2FyYWp1JTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1ZvaWNlYmlyZCUyMG5ld3MlNUMlNUNmbGlwLW5ld3MlNUMlNUNjb21wb25lbnRzJTVDJTVDaGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkhlYWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNuYWdhcmFqdSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNWb2ljZWJpcmQlMjBuZXdzJTVDJTVDZmxpcC1uZXdzJTVDJTVDY29tcG9uZW50cyU1QyU1Q25ld3MtZ3JpZC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJOZXdzR3JpZCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXlLO0FBQ3pLO0FBQ0EsMEpBQW9LO0FBQ3BLO0FBQ0EsZ0tBQXlLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBZEJhbm5lclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hZ2FyYWp1XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXFZvaWNlYmlyZCBuZXdzXFxcXGZsaXAtbmV3c1xcXFxjb21wb25lbnRzXFxcXGFkLWJhbm5lci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkhlYWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hZ2FyYWp1XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXFZvaWNlYmlyZCBuZXdzXFxcXGZsaXAtbmV3c1xcXFxjb21wb25lbnRzXFxcXGhlYWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIk5ld3NHcmlkXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFnYXJhanVcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcVm9pY2ViaXJkIG5ld3NcXFxcZmxpcC1uZXdzXFxcXGNvbXBvbmVudHNcXFxcbmV3cy1ncmlkLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cad-banner.tsx%22%2C%22ids%22%3A%5B%22AdBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cnews-grid.tsx%22%2C%22ids%22%3A%5B%22NewsGrid%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(rsc)/./components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hZ2FyYWp1JTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1ZvaWNlYmlyZCUyMG5ld3MlNUMlNUNmbGlwLW5ld3MlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNuYWdhcmFqdSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNWb2ljZWJpcmQlMjBuZXdzJTVDJTVDZmxpcC1uZXdzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hZ2FyYWp1JTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1ZvaWNlYmlyZCUyMG5ld3MlNUMlNUNmbGlwLW5ld3MlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQTBLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlcnNcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWdhcmFqdVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxWb2ljZWJpcmQgbmV3c1xcXFxmbGlwLW5ld3NcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWdhcmFqdVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxWb2ljZWJpcmQgbmV3c1xcZmxpcC1uZXdzXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./components/ad-banner.tsx":
/*!**********************************!*\
  !*** ./components/ad-banner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdBanner: () => (/* binding */ AdBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ AdBanner auto */ \n\n\n\nfunction AdBanner() {\n    const [ad, setAd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdBanner.useEffect\": ()=>{\n            fetchBannerAd();\n        }\n    }[\"AdBanner.useEffect\"], []);\n    const fetchBannerAd = async ()=>{\n        try {\n            const response = await fetch(\"/api/public/ads/banner\");\n            if (response.ok) {\n                const data = await response.json();\n                setAd(data.ad);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch banner ad:\", error);\n            // Sample ad for demo\n            setAd({\n                id: \"banner-1\",\n                title: \"Special Holiday Sale - Up to 70% Off!\",\n                image: \"https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=800&h=200&fit=crop\",\n                link: \"#\"\n            });\n        }\n    };\n    const handleClick = async ()=>{\n        if (!ad) return;\n        try {\n            // Track ad click\n            await fetch(`/api/ads/${ad.id}/click`, {\n                method: \"POST\"\n            });\n        } catch (error) {\n            console.error(\"Failed to track ad click:\", error);\n        }\n        // Open link\n        if (ad.link && ad.link !== \"#\") {\n            window.open(ad.link, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    const handleClose = ()=>{\n        setIsVisible(false);\n    };\n    if (!ad || !isVisible) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative mb-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg overflow-hidden border border-blue-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"cursor-pointer\",\n                onClick: handleClick,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-32 md:h-40\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: ad.image,\n                            alt: ad.title,\n                            fill: true,\n                            className: \"object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg md:text-xl font-bold mb-2\",\n                                        children: ad.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm opacity-90\",\n                                        children: \"Click to learn more\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleClose,\n                className: \"absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1 hover:bg-opacity-70 transition-colors\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-2 left-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                    children: \"Advertisement\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-banner.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ad-banner.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ad-card.tsx":
/*!********************************!*\
  !*** ./components/ad-card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdCard: () => (/* binding */ AdCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ AdCard auto */ \n\nfunction AdCard({ ad }) {\n    const handleClick = async ()=>{\n        try {\n            // Track ad click\n            await fetch(`/api/ads/${ad.id}/click`, {\n                method: \"POST\"\n            });\n        } catch (error) {\n            console.error(\"Failed to track ad click:\", error);\n        }\n        // Open link\n        if (ad.link && ad.link !== \"#\") {\n            window.open(ad.link, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-r from-yellow-100 to-yellow-200 rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow border-2 border-yellow-300\",\n        onClick: handleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-48\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: ad.image,\n                        alt: ad.title,\n                        fill: true,\n                        className: \"object-cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-card.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                            children: \"Ad\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-card.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-card.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-card.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-900 text-sm\",\n                        children: ad.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-card.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-600 mt-1\",\n                        children: \"Sponsored Content\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-card.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-card.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\ad-card.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ad-card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/header.tsx":
/*!*******************************!*\
  !*** ./components/header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Plus,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const categories = [\n        \"Breaking\",\n        \"Politics\",\n        \"Technology\",\n        \"Sports\",\n        \"Entertainment\",\n        \"Business\",\n        \"Health\",\n        \"Science\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-lg\",\n                                        children: \"F\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Flip News\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-6\",\n                            children: session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    session.user.role === \"ADMIN\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/admin\",\n                                        className: \"flex items-center space-x-1 text-gray-600 hover:text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Admin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/reporter\",\n                                        className: \"flex items-center space-x-1 text-gray-600 hover:text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Create\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700\",\n                                                children: session.user.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)(),\n                                        className: \"flex items-center space-x-1 text-gray-600 hover:text-red-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/signin\",\n                                        className: \"text-gray-600 hover:text-blue-600\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/signup\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\",\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 27\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 55\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:flex items-center space-x-6 py-3 border-t\",\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: `/category/${category.toLowerCase()}`,\n                            className: \"text-sm text-gray-600 hover:text-blue-600 whitespace-nowrap\",\n                            children: category\n                        }, category, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: `/category/${category.toLowerCase()}`,\n                                        className: \"text-sm text-gray-600 hover:text-blue-600 p-2\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-4\",\n                                children: session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-2\",\n                                    children: [\n                                        session.user.role === \"ADMIN\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/admin\",\n                                            className: \"flex items-center space-x-2 text-gray-600 hover:text-blue-600 p-2\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Admin Panel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/reporter\",\n                                            className: \"flex items-center space-x-2 text-gray-600 hover:text-blue-600 p-2\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Create News\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)();\n                                                setIsMenuOpen(false);\n                                            },\n                                            className: \"flex items-center space-x-2 text-gray-600 hover:text-red-600 p-2 text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signin\",\n                                            className: \"text-gray-600 hover:text-blue-600 p-2\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signup\",\n                                            className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-center\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\header.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/news-card.tsx":
/*!**********************************!*\
  !*** ./components/news-card.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewsCard: () => (/* binding */ NewsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _share_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./share-button */ \"(ssr)/./components/share-button.tsx\");\n/* __next_internal_client_entry_do_not_use__ NewsCard auto */ \n\n\n\n\n\nfunction NewsCard({ news }) {\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likes, setLikes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(news.likes);\n    const handleLike = async ()=>{\n        try {\n            const response = await fetch(`/api/news/${news.id}/like`, {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                setIsLiked(!isLiked);\n                setLikes((prev)=>isLiked ? prev - 1 : prev + 1);\n            }\n        } catch (error) {\n            console.error(\"Failed to like news:\", error);\n            // Optimistic update for demo\n            setIsLiked(!isLiked);\n            setLikes((prev)=>isLiked ? prev - 1 : prev + 1);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return \"Just now\";\n        if (diffInHours < 24) return `${diffInHours}h ago`;\n        return date.toLocaleDateString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group relative w-full cursor-pointer perspective-1000\",\n        onClick: ()=>setIsFlipped(!isFlipped),\n        id: `news-card-${news.id}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative w-full transition-transform duration-700 transform-style-preserve-3d ${isFlipped ? \"rotate-y-180\" : \"\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 w-full backface-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-gradient-to-r from-red-500 to-red-600 px-4 py-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1 left-0 right-0 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                ...Array(8)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-300 text-xs\",\n                                                    children: \"⭐\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: news.author.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-lg font-bold\",\n                                                children: \"Flip News\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-48 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: news.image,\n                                    alt: news.title,\n                                    fill: true,\n                                    className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gradient-to-b from-white to-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-gray-900 mb-3 leading-tight\",\n                                        children: news.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 text-sm leading-relaxed mb-4 line-clamp-4\",\n                                        children: news.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 bottom-0 w-20 h-20 opacity-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full rounded-full border-4 border-gray-300 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-xs font-bold\",\n                                                        children: \"FLIP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_share_button__WEBPACK_IMPORTED_MODULE_4__.ShareButton, {\n                                                    news: news,\n                                                    onShare: (e)=>e.stopPropagation()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 w-full h-full backface-hidden rotate-y-180\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-red-500 to-red-700 rounded-lg shadow-lg h-full flex flex-col text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-red-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm opacity-90\",\n                                            children: \"Full Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold\",\n                                            children: \"Flip News\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 flex flex-col justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold mb-4 text-center leading-tight\",\n                                        children: news.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-center mb-6 opacity-90 leading-relaxed\",\n                                        children: news.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-around mb-6 text-sm bg-red-600 bg-opacity-50 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: `w-5 h-5 mb-1 ${isLiked ? \"fill-current text-red-200\" : \"\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: likes\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: news.views\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: news.shares\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleLike();\n                                                },\n                                                className: `flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors ${isLiked ? \"bg-red-200 text-red-800\" : \"bg-white text-red-600 hover:bg-red-50\"}`,\n                                                children: isLiked ? \"Liked\" : \"Like\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: `/news/${news.id}`,\n                                                className: \"flex-1 bg-white text-red-600 py-2 px-4 rounded-full text-sm font-medium hover:bg-red-50 transition-colors text-center\",\n                                                onClick: (e)=>e.stopPropagation(),\n                                                children: \"Read More\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-red-400 text-center text-sm opacity-75\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formatDate(news.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"By \",\n                                                news.author.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/news-card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/news-grid.tsx":
/*!**********************************!*\
  !*** ./components/news-grid.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewsGrid: () => (/* binding */ NewsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _news_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./news-card */ \"(ssr)/./components/news-card.tsx\");\n/* harmony import */ var _ad_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ad-card */ \"(ssr)/./components/ad-card.tsx\");\n/* harmony import */ var _lib_seed_data__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/seed-data */ \"(ssr)/./lib/seed-data.ts\");\n/* __next_internal_client_entry_do_not_use__ NewsGrid auto */ \n\n\n\n\nfunction NewsGrid() {\n    const [news, setNews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewsGrid.useEffect\": ()=>{\n            fetchNews();\n            fetchAds();\n        }\n    }[\"NewsGrid.useEffect\"], []);\n    const fetchNews = async ()=>{\n        try {\n            const response = await fetch(\"/api/public/news\");\n            if (response.ok) {\n                const data = await response.json();\n                setNews(data.news || []);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch news:\", error);\n            // For demo purposes, show Telugu sample data\n            setNews(_lib_seed_data__WEBPACK_IMPORTED_MODULE_4__.sampleTeluguNews);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAds = async ()=>{\n        try {\n            const response = await fetch(\"/api/public/ads\");\n            if (response.ok) {\n                const data = await response.json();\n                setAds(data.ads || []);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch ads:\", error);\n            // For demo purposes, show sample ads\n            setAds(sampleAds);\n        }\n    };\n    // Sample data for demo\n    const sampleNews = [\n        {\n            id: \"1\",\n            title: \"Breaking: Major Technology Breakthrough Announced\",\n            excerpt: \"Scientists have made a significant discovery that could revolutionize the tech industry...\",\n            image: \"https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop\",\n            category: \"Technology\",\n            createdAt: new Date().toISOString(),\n            views: 1250,\n            likes: 89,\n            shares: 23,\n            author: {\n                name: \"Tech Reporter\"\n            }\n        },\n        {\n            id: \"2\",\n            title: \"Sports Update: Championship Finals This Weekend\",\n            excerpt: \"The most anticipated match of the season is set to take place this weekend...\",\n            image: \"https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=400&h=300&fit=crop\",\n            category: \"Sports\",\n            createdAt: new Date(Date.now() - 3600000).toISOString(),\n            views: 890,\n            likes: 67,\n            shares: 15,\n            author: {\n                name: \"Sports Desk\"\n            }\n        },\n        {\n            id: \"3\",\n            title: \"Economic Markets Show Strong Growth\",\n            excerpt: \"Financial analysts report positive trends in the global markets this quarter...\",\n            image: \"https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400&h=300&fit=crop\",\n            category: \"Business\",\n            createdAt: new Date(Date.now() - 7200000).toISOString(),\n            views: 654,\n            likes: 45,\n            shares: 12,\n            author: {\n                name: \"Business Reporter\"\n            }\n        }\n    ];\n    const sampleAds = [\n        {\n            id: \"1\",\n            title: \"Special Offer - 50% Off\",\n            image: \"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=200&fit=crop\",\n            link: \"#\"\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n            children: [\n                ...Array(6)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-300 rounded-lg h-48 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-300 rounded mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-300 rounded w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    // Insert ads every 3 news items\n    const contentItems = [];\n    let adIndex = 0;\n    for(let i = 0; i < news.length; i++){\n        contentItems.push({\n            type: \"news\",\n            data: news[i]\n        });\n        // Insert ad every 3 items\n        if ((i + 1) % 3 === 0 && adIndex < ads.length) {\n            contentItems.push({\n                type: \"ad\",\n                data: ads[adIndex]\n            });\n            adIndex++;\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: contentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.type === \"news\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_news_card__WEBPACK_IMPORTED_MODULE_2__.NewsCard, {\n                    news: item.data\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ad_card__WEBPACK_IMPORTED_MODULE_3__.AdCard, {\n                    ad: item.data\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 13\n                }, this)\n            }, `${item.type}-${index}`, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/news-grid.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWlEO0FBTzFDLFNBQVNDLFVBQVUsRUFBRUMsUUFBUSxFQUFrQjtJQUNwRCxxQkFDRSw4REFBQ0YsNERBQWVBO2tCQUNiRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFnYXJhanVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVm9pY2ViaXJkIG5ld3NcXGZsaXAtbmV3c1xcY29tcG9uZW50c1xccHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgfSBmcm9tIFwibmV4dC1hdXRoL3JlYWN0XCJcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gXCJyZWFjdFwiXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/share-button.tsx":
/*!*************************************!*\
  !*** ./components/share-button.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShareButton: () => (/* binding */ ShareButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Download,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Download,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Download,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Download,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html2canvas */ \"(ssr)/./node_modules/html2canvas/dist/html2canvas.esm.js\");\n/* __next_internal_client_entry_do_not_use__ ShareButton auto */ \n\n\n\nfunction ShareButton({ news, onShare }) {\n    const [isSharing, setIsSharing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const shareUrl = `${window.location.origin}/news/${news.id}`;\n    const shareText = `Check out this news: ${news.title}`;\n    const handleShare = async (e)=>{\n        e.stopPropagation();\n        onShare?.(e);\n        setIsSharing(true);\n        try {\n            // Try Web Share API first\n            if (navigator.share) {\n                await navigator.share({\n                    title: news.title,\n                    text: shareText,\n                    url: shareUrl\n                });\n            } else {\n                // Fallback to screenshot sharing\n                await shareWithScreenshot();\n            }\n        } catch (error) {\n            console.error(\"Share failed:\", error);\n            // Fallback to copy link\n            await copyToClipboard();\n        } finally{\n            setIsSharing(false);\n        }\n    };\n    const shareWithScreenshot = async ()=>{\n        try {\n            const cardElement = document.getElementById(`news-card-${news.id}`);\n            if (!cardElement) return;\n            // Create canvas from the news card\n            const canvas = await (0,html2canvas__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cardElement, {\n                backgroundColor: \"#ffffff\",\n                scale: 2,\n                logging: false,\n                useCORS: true\n            });\n            // Add watermark and branding\n            const ctx = canvas.getContext(\"2d\");\n            if (ctx) {\n                // Add watermark\n                ctx.fillStyle = \"rgba(0, 0, 0, 0.7)\";\n                ctx.fillRect(0, canvas.height - 60, canvas.width, 60);\n                ctx.fillStyle = \"#ffffff\";\n                ctx.font = \"bold 24px Arial\";\n                ctx.fillText(\"Flip News\", 20, canvas.height - 30);\n                ctx.font = \"16px Arial\";\n                ctx.fillText(shareUrl, 20, canvas.height - 10);\n            }\n            // Convert to blob\n            canvas.toBlob(async (blob)=>{\n                if (!blob) return;\n                try {\n                    // Try to share the image\n                    if (navigator.share && navigator.canShare) {\n                        const file = new File([\n                            blob\n                        ], `${news.title.slice(0, 30)}.png`, {\n                            type: \"image/png\"\n                        });\n                        if (navigator.canShare({\n                            files: [\n                                file\n                            ]\n                        })) {\n                            await navigator.share({\n                                title: news.title,\n                                text: shareText,\n                                files: [\n                                    file\n                                ]\n                            });\n                            return;\n                        }\n                    }\n                    // Fallback: Download the image\n                    const url = URL.createObjectURL(blob);\n                    const a = document.createElement(\"a\");\n                    a.href = url;\n                    a.download = `${news.title.slice(0, 30)}.png`;\n                    document.body.appendChild(a);\n                    a.click();\n                    document.body.removeChild(a);\n                    URL.revokeObjectURL(url);\n                    // Also copy the link\n                    await copyToClipboard();\n                } catch (error) {\n                    console.error(\"Screenshot share failed:\", error);\n                    await copyToClipboard();\n                }\n            }, \"image/png\");\n        } catch (error) {\n            console.error(\"Screenshot generation failed:\", error);\n            await copyToClipboard();\n        }\n    };\n    const copyToClipboard = async ()=>{\n        try {\n            await navigator.clipboard.writeText(shareUrl);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (error) {\n            console.error(\"Copy failed:\", error);\n        }\n    };\n    const handleCopyLink = async (e)=>{\n        e.stopPropagation();\n        await copyToClipboard();\n    };\n    const handleDownloadScreenshot = async (e)=>{\n        e.stopPropagation();\n        setIsSharing(true);\n        await shareWithScreenshot();\n        setIsSharing(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-end\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShare,\n                disabled: isSharing,\n                className: \"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md transition-colors disabled:opacity-50 flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Share\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    isSharing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden group-hover:flex items-center space-x-2 ml-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDownloadScreenshot,\n                        disabled: isSharing,\n                        className: \"p-2 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full disabled:opacity-50 transition-colors\",\n                        title: \"Download as image\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopyLink,\n                        className: \"p-2 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full transition-colors\",\n                        title: \"Copy link\",\n                        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/share-button.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/seed-data.ts":
/*!**************************!*\
  !*** ./lib/seed-data.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   sampleTeluguNews: () => (/* binding */ sampleTeluguNews)\n/* harmony export */ });\nconst sampleTeluguNews = [\n    {\n        id: \"1\",\n        title: \"పెదలకు గూడు కట్టించడమే కాంగ్రెస్ లక్ష్యం\",\n        excerpt: \"హుజూరాబాద్ జిల్లా హుసన్ పల్లి మండలంలో వందలాది కుటుంబాలు ఇళ్లు కట్టించుకోవడానికి 200 మంది ఇంజినీర్లు ఇళ్లు మంజూరు పత్రాలు పంపిణీ చేశారు. ఈ ఇంద్రు పెదలకు ఆత్మగౌరవ ప్రతిష్ఠగా అభివృద్ధి చేయాలని. 5 లక్షల ఆర్థిక సహాయం నాలుగు దశల్లో లభించనుందని పేర్కొన్నారు. ఈ సందర్భంగా రైతులకు వివిధ్యాల కూడా పంపిణీ చేశారు.\",\n        image: \"https://images.unsplash.com/photo-1586339949916-3e9457bef6d3?w=800&h=600&fit=crop\",\n        category: \"Politics\",\n        createdAt: \"2024-01-15T10:30:00Z\",\n        views: 1250,\n        likes: 89,\n        shares: 23,\n        author: {\n            name: \"Adimulam Dileep\"\n        }\n    },\n    {\n        id: \"2\",\n        title: \"రైతులకు ఉచిత విద్యుత్ సరఫరా కొనసాగింపు\",\n        excerpt: \"తెలంగాణ రాష్ట్రంలో రైతులకు ఉచిత విద్యుత్ సరఫరా కొనసాగిస్తామని ముఖ్యమంత్రి రేవంత్ రెడ్డి హామీ ఇచ్చారు. వ్యవసాయ రంగ అభివృద్ధికి ప్రభుత్వం కట్టుబడి ఉందని తెలిపారు. రైతుల సమస్యలను పరిష్కరించడానికి ప్రత్యేక కమిటీలను ఏర్పాటు చేస్తామని వెల్లడించారు.\",\n        image: \"https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=800&h=600&fit=crop\",\n        category: \"Agriculture\",\n        createdAt: \"2024-01-15T08:15:00Z\",\n        views: 2100,\n        likes: 156,\n        shares: 45,\n        author: {\n            name: \"Srinivas Reddy\"\n        }\n    },\n    {\n        id: \"3\",\n        title: \"హైదరాబాద్‌లో మెట్రో రైలు సేవలు విస్తరణ\",\n        excerpt: \"హైదరాబాద్ మెట్రో రైలు సేవలను మరింత విస్తరించనున్నట్లు ప్రభుత్వం ప్రకటించింది. కొత్త రూట్లు మరియు స్టేషన్లను జోడించి ప్రజలకు మెరుగైన రవాణా సౌకర్యాలను అందించనున్నారు. దీనితో ట్రాఫిక్ సమస్యలు తగ్గుతాయని అధికారులు భావిస్తున్నారు.\",\n        image: \"https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=800&h=600&fit=crop\",\n        category: \"Transport\",\n        createdAt: \"2024-01-15T06:45:00Z\",\n        views: 1800,\n        likes: 134,\n        shares: 67,\n        author: {\n            name: \"Madhavi Latha\"\n        }\n    },\n    {\n        id: \"4\",\n        title: \"విద్యా రంగంలో కొత్త సంస్కరణలు\",\n        excerpt: \"తెలంగాణ రాష్ట్రంలో విద్యా రంగంలో కొత్త సంస్కరణలను ప్రవేశపెట్టనున్నట్లు విద్యా మంత్రి ప్రకటించారు. డిజిటల్ విద్య, ఉచిత పుస్తకాలు మరియు మిడ్ డే మీల్ పథకాలను మెరుగుపరచనున్నారు. ప్రభుత్వ పాఠశాలల్లో మౌలిక సదుపాయాలను అభివృద్ధి పరుస్తామని హామీ ఇచ్చారు.\",\n        image: \"https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=800&h=600&fit=crop\",\n        category: \"Education\",\n        createdAt: \"2024-01-14T16:20:00Z\",\n        views: 950,\n        likes: 78,\n        shares: 34,\n        author: {\n            name: \"Ramesh Kumar\"\n        }\n    },\n    {\n        id: \"5\",\n        title: \"ఆరోగ్య రంగంలో కొత్త పథకాలు\",\n        excerpt: \"రాష్ట్రంలో ఆరోగ్య రంగ అభివృద్ధికి కొత్త పథకాలను ప్రవేశపెట్టనున్నట్లు ఆరోగ్య మంత్రి తెలిపారు. ప్రాథమిక ఆరోగ్య కేంద్రాలను మెరుగుపరచడం, వైద్య సిబ్బంది నియామకాలు మరియు ఉచిత వైద్య సేవలను విస్తరించనున్నారు. గ్రామీణ ప్రాంతాల్లో మొబైల్ వైద్య యూనిట్లను ప్రారంభించనున్నారు.\",\n        image: \"https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop\",\n        category: \"Health\",\n        createdAt: \"2024-01-14T14:10:00Z\",\n        views: 1650,\n        likes: 112,\n        shares: 28,\n        author: {\n            name: \"Priya Sharma\"\n        }\n    },\n    {\n        id: \"6\",\n        title: \"క్రీడా రంగ అభివృద్ధికి కొత్త కార్యక్రమాలు\",\n        excerpt: \"తెలంగాణ రాష్ట్రంలో క్రీడా రంగ అభివృద్ధికి కొత్త కార్యక్రమాలను ప్రారంభించనున్నట్లు క్రీడా మంత్రి ప్రకటించారు. యువతకు క్రీడా సౌకర్యాలను అందించడం, కోచింగ్ సెంటర్లు స్థాపించడం మరియు క్రీడాకారులకు ప్రోత్సాహకాలు అందించనున్నారు. అంతర్జాతీయ స్థాయి క్రీడా సముదాయాలను నిర్మించనున్నారు.\",\n        image: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop\",\n        category: \"Sports\",\n        createdAt: \"2024-01-14T12:30:00Z\",\n        views: 1320,\n        likes: 95,\n        shares: 41,\n        author: {\n            name: \"Venkat Rao\"\n        }\n    }\n];\nconst categories = [\n    \"Politics\",\n    \"Agriculture\",\n    \"Transport\",\n    \"Education\",\n    \"Health\",\n    \"Sports\",\n    \"Technology\",\n    \"Entertainment\",\n    \"Business\",\n    \"Culture\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/seed-data.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cad-banner.tsx%22%2C%22ids%22%3A%5B%22AdBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cnews-grid.tsx%22%2C%22ids%22%3A%5B%22NewsGrid%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cad-banner.tsx%22%2C%22ids%22%3A%5B%22AdBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cnews-grid.tsx%22%2C%22ids%22%3A%5B%22NewsGrid%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ad-banner.tsx */ \"(ssr)/./components/ad-banner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/header.tsx */ \"(ssr)/./components/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/news-grid.tsx */ \"(ssr)/./components/news-grid.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hZ2FyYWp1JTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1ZvaWNlYmlyZCUyMG5ld3MlNUMlNUNmbGlwLW5ld3MlNUMlNUNjb21wb25lbnRzJTVDJTVDYWQtYmFubmVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkFkQmFubmVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hZ2FyYWp1JTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1ZvaWNlYmlyZCUyMG5ld3MlNUMlNUNmbGlwLW5ld3MlNUMlNUNjb21wb25lbnRzJTVDJTVDaGVhZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkhlYWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNuYWdhcmFqdSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNWb2ljZWJpcmQlMjBuZXdzJTVDJTVDZmxpcC1uZXdzJTVDJTVDY29tcG9uZW50cyU1QyU1Q25ld3MtZ3JpZC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJOZXdzR3JpZCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXlLO0FBQ3pLO0FBQ0EsMEpBQW9LO0FBQ3BLO0FBQ0EsZ0tBQXlLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBZEJhbm5lclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hZ2FyYWp1XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXFZvaWNlYmlyZCBuZXdzXFxcXGZsaXAtbmV3c1xcXFxjb21wb25lbnRzXFxcXGFkLWJhbm5lci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkhlYWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hZ2FyYWp1XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXFZvaWNlYmlyZCBuZXdzXFxcXGZsaXAtbmV3c1xcXFxjb21wb25lbnRzXFxcXGhlYWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIk5ld3NHcmlkXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFnYXJhanVcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcVm9pY2ViaXJkIG5ld3NcXFxcZmxpcC1uZXdzXFxcXGNvbXBvbmVudHNcXFxcbmV3cy1ncmlkLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cad-banner.tsx%22%2C%22ids%22%3A%5B%22AdBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cnews-grid.tsx%22%2C%22ids%22%3A%5B%22NewsGrid%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hZ2FyYWp1JTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1ZvaWNlYmlyZCUyMG5ld3MlNUMlNUNmbGlwLW5ld3MlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNuYWdhcmFqdSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNWb2ljZWJpcmQlMjBuZXdzJTVDJTVDZmxpcC1uZXdzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hZ2FyYWp1JTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1ZvaWNlYmlyZCUyMG5ld3MlNUMlNUNmbGlwLW5ld3MlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQTBLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlcnNcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWdhcmFqdVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxWb2ljZWJpcmQgbmV3c1xcXFxmbGlwLW5ld3NcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnagaraju%5C%5CDocuments%5C%5Caugment-projects%5C%5CVoicebird%20news%5C%5Cflip-news%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/lucide-react","vendor-chunks/html2canvas"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnagaraju%5CDocuments%5Caugment-projects%5CVoicebird%20news%5Cflip-news%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnagaraju%5CDocuments%5Caugment-projects%5CVoicebird%20news%5Cflip-news&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
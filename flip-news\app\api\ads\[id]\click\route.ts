import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const ad = await prisma.ad.findUnique({
      where: { id: params.id }
    })

    if (!ad) {
      return NextResponse.json(
        { error: "Ad not found" },
        { status: 404 }
      )
    }

    // Increment click count
    await prisma.ad.update({
      where: { id: params.id },
      data: {
        clicks: {
          increment: 1
        }
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to track ad click:", error)
    return NextResponse.json(
      { error: "Failed to track ad click" },
      { status: 500 }
    )
  }
}

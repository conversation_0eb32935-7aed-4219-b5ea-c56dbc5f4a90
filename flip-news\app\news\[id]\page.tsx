"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, Heart, Share2, Eye, Clock, User } from "lucide-react"
import { ShareButton } from "@/components/share-button"

interface NewsDetail {
  id: string
  title: string
  content: string
  excerpt: string
  image: string
  category: string
  tags: string[]
  views: number
  likes: number
  shares: number
  createdAt: string
  publishedAt: string
  author: {
    name: string
    avatar?: string
  }
}

export default function NewsDetailPage() {
  const params = useParams()
  const [news, setNews] = useState<NewsDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [isLiked, setIsLiked] = useState(false)
  const [likes, setLikes] = useState(0)

  useEffect(() => {
    if (params.id) {
      fetchNews(params.id as string)
    }
  }, [params.id])

  const fetchNews = async (id: string) => {
    try {
      const response = await fetch(`/api/public/news/${id}`)
      if (response.ok) {
        const data = await response.json()
        setNews(data.news)
        setLikes(data.news.likes)
      } else {
        // Sample data for demo
        setNews({
          id: "1",
          title: "Breaking: Major Technology Breakthrough Announced",
          content: `Scientists at leading research institutions have announced a groundbreaking discovery that could revolutionize the technology industry. The breakthrough involves advanced quantum computing capabilities that promise to solve complex problems exponentially faster than current systems.

The research team, led by Dr. Emily Chen, has successfully demonstrated quantum supremacy in practical applications, moving beyond theoretical possibilities to real-world implementations. This development could have far-reaching implications for fields ranging from cryptography to drug discovery.

"This represents a paradigm shift in how we approach computational challenges," said Dr. Chen during the announcement. "We're not just talking about incremental improvements, but a fundamental change in what's possible."

The technology is expected to be commercially available within the next five years, with early applications focusing on financial modeling, climate simulation, and artificial intelligence enhancement.

Industry experts are calling this one of the most significant technological advances of the decade, comparing its potential impact to the invention of the internet or the development of the microprocessor.

The implications for cybersecurity are particularly significant, as quantum computing could both threaten current encryption methods and provide new ways to secure digital communications. Governments and corporations are already investing heavily in quantum-resistant security measures.

Educational institutions are also preparing for this shift, with new quantum computing programs being launched at universities worldwide. The demand for quantum computing specialists is expected to grow exponentially in the coming years.

This breakthrough represents years of collaborative research and billions of dollars in investment. The next phase will focus on scaling the technology and making it accessible to a broader range of applications and users.`,
          excerpt: "Scientists announce a revolutionary quantum computing breakthrough that could transform multiple industries within the next five years.",
          image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop",
          category: "Technology",
          tags: ["quantum computing", "breakthrough", "science", "technology"],
          views: 2547,
          likes: 189,
          shares: 67,
          createdAt: new Date().toISOString(),
          publishedAt: new Date().toISOString(),
          author: {
            name: "Tech Reporter",
            avatar: ""
          }
        })
        setLikes(189)
      }
    } catch (error) {
      console.error("Failed to fetch news:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async () => {
    if (!news) return

    try {
      const response = await fetch(`/api/news/${news.id}/like`, {
        method: "POST"
      })
      
      if (response.ok) {
        setIsLiked(!isLiked)
        setLikes(prev => isLiked ? prev - 1 : prev + 1)
      }
    } catch (error) {
      console.error("Failed to like news:", error)
      // Optimistic update for demo
      setIsLiked(!isLiked)
      setLikes(prev => isLiked ? prev - 1 : prev + 1)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    return date.toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!news) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">News not found</h1>
          <Link href="/" className="text-blue-600 hover:text-blue-800">
            Return to homepage
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href="/"
            className="inline-flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to News
          </Link>
        </div>
      </div>

      <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Article Header */}
        <header className="mb-8">
          <div className="mb-4">
            <span className="inline-block bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
              {news.category}
            </span>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
            {news.title}
          </h1>

          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4" />
                <span>{news.author.name}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>{formatDate(news.publishedAt)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Eye className="w-4 h-4" />
                <span>{news.views.toLocaleString()} views</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={handleLike}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  isLiked 
                    ? "bg-red-100 text-red-600" 
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                <Heart className={`w-4 h-4 ${isLiked ? "fill-current" : ""}`} />
                <span>{likes}</span>
              </button>

              <ShareButton 
                news={{
                  id: news.id,
                  title: news.title,
                  excerpt: news.excerpt,
                  image: news.image,
                  category: news.category
                }}
              />
            </div>
          </div>

          {/* Featured Image */}
          {news.image && (
            <div className="relative h-64 md:h-96 rounded-lg overflow-hidden mb-8">
              <Image
                src={news.image}
                alt={news.title}
                fill
                className="object-cover"
              />
            </div>
          )}
        </header>

        {/* Article Content */}
        <div className="prose prose-lg max-w-none">
          {news.content.split('\n\n').map((paragraph, index) => (
            <p key={index} className="mb-6 text-gray-800 leading-relaxed">
              {paragraph}
            </p>
          ))}
        </div>

        {/* Tags */}
        {news.tags && news.tags.length > 0 && (
          <div className="mt-8 pt-8 border-t border-gray-200">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Tags:</h3>
            <div className="flex flex-wrap gap-2">
              {news.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-block bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>
        )}
      </article>
    </div>
  )
}

# 📰 Flip News - Way2News Style Web App

A mobile-first short news web app with screenshot sharing, reporter & admin roles, ad management, public API, and full brand customization.

## ⚙️ Tech Stack

- **Frontend**: Next.js 14 (App Router), TypeScript, Tailwind CSS
- **Authentication**: NextAuth.js with role-based access (Admin/Reporter)
- **Database**: PostgreSQL with Prisma ORM
- **Screenshot**: html2canvas for news card sharing
- **Icons**: Lucide React
- **Hosting**: Vercel-ready

## 🚀 Features

### 👤 User Roles
- **Admin**: Full access, news/ad/user management, analytics, branding
- **Reporter**: Create/edit own news, view analytics
- **Public**: Browse news, like, share with screenshots

### 📰 News System
- Rich text content with images
- Categories and tags
- Draft/Published status
- View/like/share analytics
- Mobile-first responsive design

### 📸 Screenshot Sharing
- Click news cards to flip and reveal details
- Share button captures card as image with watermark
- Web Share API integration with fallbacks
- Download option for sharing

### 📢 Ad Management
- Image/video ads with scheduling
- Frequency control (show every X news)
- Click tracking and analytics
- Banner and inline ad placements

### 🎨 Branding & Customization
- App name and logo upload
- Primary/secondary color themes
- Custom watermark for shares
- Configurable share messages

### 📡 Public API
- `/api/public/news` - Get all published news
- `/api/public/news/:id` - Get specific news
- `/api/public/ads` - Get active ads
- Rate limiting and pagination ready

## 🛠️ Setup Instructions

### 1. Clone and Install
```bash
git clone <repository-url>
cd flip-news
npm install
```

### 2. Environment Setup
Create `.env.local` file:
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/flipnews?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# App Configuration
APP_NAME="Flip News"
APP_URL="http://localhost:3000"
```

### 3. Database Setup
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed with sample data
npm run db:seed
```

### 4. Run Development Server
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the app.

## 👥 Demo Accounts

After seeding, use these accounts:

- **Admin**: <EMAIL> / admin123
- **Reporter**: <EMAIL> / reporter123
- **Reporter 2**: <EMAIL> / reporter123

## 📱 Key Features Demo

1. **Homepage**: Browse news with flip animations
2. **News Cards**: Click to flip and see details
3. **Share Feature**: Use share button to capture screenshots
4. **Admin Panel**: `/admin` - Manage everything
5. **Reporter Dashboard**: `/reporter` - Create and manage news
6. **Authentication**: Role-based access control

## 🔧 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:seed      # Seed database with sample data
npm run db:studio    # Open Prisma Studio
```

## 📂 Project Structure

```
flip-news/
├── app/
│   ├── admin/           # Admin dashboard
│   ├── reporter/        # Reporter dashboard
│   ├── auth/           # Authentication pages
│   ├── news/[id]/      # News detail pages
│   ├── api/            # API routes
│   └── page.tsx        # Homepage
├── components/         # Reusable UI components
├── lib/               # Utilities (auth, prisma)
├── prisma/            # Database schema & seed
├── public/            # Static assets
└── types/             # TypeScript definitions
```

## 🌐 API Endpoints

### Public API
- `GET /api/public/news` - List published news
- `GET /api/public/news/:id` - Get news details
- `GET /api/public/ads` - Get active ads

### Protected API
- `POST /api/reporter/news` - Create news (Reporter+)
- `PUT /api/reporter/news/:id` - Update news (Reporter+)
- `DELETE /api/reporter/news/:id` - Delete news (Reporter+)
- `POST /api/news/:id/like` - Like news
- `POST /api/ads/:id/click` - Track ad clicks

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push

### Database
- Use Vercel Postgres, Supabase, or any PostgreSQL provider
- Update `DATABASE_URL` in environment variables

## 🎨 Customization

### Branding
- Update app settings in admin panel
- Modify colors in `tailwind.config.js`
- Replace logo in `public/` directory

### Features
- Add new news categories in seed data
- Customize ad frequency and placement
- Extend user roles and permissions

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

---

Built with ❤️ using Next.js 14, Prisma, and Tailwind CSS

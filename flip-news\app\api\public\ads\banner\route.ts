import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const now = new Date()

    const ad = await prisma.ad.findFirst({
      where: {
        active: true,
        OR: [
          {
            startDate: null,
            endDate: null
          },
          {
            startDate: {
              lte: now
            },
            endDate: {
              gte: now
            }
          },
          {
            startDate: {
              lte: now
            },
            endDate: null
          },
          {
            startDate: null,
            endDate: {
              gte: now
            }
          }
        ]
      },
      orderBy: {
        createdAt: "desc"
      }
    })

    return NextResponse.json({ ad })
  } catch (error) {
    console.error("Failed to fetch banner ad:", error)
    return NextResponse.json(
      { error: "Failed to fetch banner ad" },
      { status: 500 }
    )
  }
}

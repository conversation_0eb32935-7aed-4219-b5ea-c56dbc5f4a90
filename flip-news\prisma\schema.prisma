// Flip News - Prisma Schema
// Mobile-first news app with screenshot sharing

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and role management
model User {
  id        String   @id @default(cuid())
  name      String
  email     String   @unique
  password  String
  role      Role     @default(REPORTER)
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  news      News[]
  accounts  Account[]
  sessions  Session[]

  @@map("users")
}

// News model - core content
model News {
  id          String      @id @default(cuid())
  title       String
  content     String      @db.Text
  excerpt     String?     @db.VarChar(300)
  image       String?
  video       String?
  category    String
  tags        String[]
  status      NewsStatus  @default(DRAFT)
  featured    <PERSON>olean     @default(false)

  // Analytics
  views       Int         @default(0)
  likes       Int         @default(0)
  shares      Int         @default(0)

  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  publishedAt DateTime?

  // Relations
  author      User        @relation(fields: [authorId], references: [id], onDelete: Cascade)
  authorId    String

  @@map("news")
  @@index([category])
  @@index([status])
  @@index([publishedAt])
}

// Advertisement model
model Ad {
  id          String    @id @default(cuid())
  title       String
  image       String?
  video       String?
  link        String?
  frequency   Int       @default(3) // Show every X news
  active      Boolean   @default(true)
  startDate   DateTime?
  endDate     DateTime?
  views       Int       @default(0)
  clicks      Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("ads")
}

// App settings and branding
model Settings {
  id              String   @id @default(cuid())
  appName         String   @default("Flip News")
  logo            String?
  watermark       String?
  primaryColor    String   @default("#3B82F6")
  secondaryColor  String   @default("#1E40AF")
  footerText      String?
  shareMessage    String   @default("Check out this news from Flip News!")
  apiKey          String?  @unique
  updatedAt       DateTime @updatedAt

  @@map("settings")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Enums
enum Role {
  ADMIN
  REPORTER
}

enum NewsStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

"use client"

import { useState } from "react"
import { Share2, Download, Co<PERSON>, Check } from "lucide-react"
import html2canvas from "html2canvas"

interface ShareButtonProps {
  news: {
    id: string
    title: string
    excerpt: string
    image: string
    category: string
  }
  onShare?: (e: React.MouseEvent) => void
}

export function ShareButton({ news, onShare }: ShareButtonProps) {
  const [isSharing, setIsSharing] = useState(false)
  const [copied, setCopied] = useState(false)

  const shareUrl = `${window.location.origin}/news/${news.id}`
  const shareText = `Check out this news: ${news.title}`

  const handleShare = async (e: React.MouseEvent) => {
    e.stopPropagation()
    onShare?.(e)
    setIsSharing(true)

    try {
      // Try Web Share API first
      if (navigator.share) {
        await navigator.share({
          title: news.title,
          text: shareText,
          url: shareUrl,
        })
      } else {
        // Fallback to screenshot sharing
        await shareWithScreenshot()
      }
    } catch (error) {
      console.error("Share failed:", error)
      // Fallback to copy link
      await copyToClipboard()
    } finally {
      setIsSharing(false)
    }
  }

  const shareWithScreenshot = async () => {
    try {
      const cardElement = document.getElementById(`news-card-${news.id}`)
      if (!cardElement) return

      // Create canvas from the news card
      const canvas = await html2canvas(cardElement, {
        backgroundColor: "#ffffff",
        scale: 2,
        logging: false,
        useCORS: true,
      })

      // Add watermark and branding
      const ctx = canvas.getContext("2d")
      if (ctx) {
        // Add watermark
        ctx.fillStyle = "rgba(0, 0, 0, 0.7)"
        ctx.fillRect(0, canvas.height - 60, canvas.width, 60)
        
        ctx.fillStyle = "#ffffff"
        ctx.font = "bold 24px Arial"
        ctx.fillText("Flip News", 20, canvas.height - 30)
        
        ctx.font = "16px Arial"
        ctx.fillText(shareUrl, 20, canvas.height - 10)
      }

      // Convert to blob
      canvas.toBlob(async (blob) => {
        if (!blob) return

        try {
          // Try to share the image
          if (navigator.share && navigator.canShare) {
            const file = new File([blob], `${news.title.slice(0, 30)}.png`, {
              type: "image/png",
            })
            
            if (navigator.canShare({ files: [file] })) {
              await navigator.share({
                title: news.title,
                text: shareText,
                files: [file],
              })
              return
            }
          }

          // Fallback: Download the image
          const url = URL.createObjectURL(blob)
          const a = document.createElement("a")
          a.href = url
          a.download = `${news.title.slice(0, 30)}.png`
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)

          // Also copy the link
          await copyToClipboard()
        } catch (error) {
          console.error("Screenshot share failed:", error)
          await copyToClipboard()
        }
      }, "image/png")
    } catch (error) {
      console.error("Screenshot generation failed:", error)
      await copyToClipboard()
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error("Copy failed:", error)
    }
  }

  const handleCopyLink = async (e: React.MouseEvent) => {
    e.stopPropagation()
    await copyToClipboard()
  }

  const handleDownloadScreenshot = async (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsSharing(true)
    await shareWithScreenshot()
    setIsSharing(false)
  }

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={handleShare}
        disabled={isSharing}
        className="flex items-center space-x-1 text-xs text-gray-500 hover:text-blue-500 disabled:opacity-50"
      >
        <Share2 className="w-4 h-4" />
        <span>{news.shares || 0}</span>
      </button>

      {/* Additional share options */}
      <div className="hidden group-hover:flex items-center space-x-1">
        <button
          onClick={handleDownloadScreenshot}
          disabled={isSharing}
          className="p-1 text-gray-400 hover:text-blue-500 disabled:opacity-50"
          title="Download as image"
        >
          <Download className="w-3 h-3" />
        </button>
        
        <button
          onClick={handleCopyLink}
          className="p-1 text-gray-400 hover:text-blue-500"
          title="Copy link"
        >
          {copied ? (
            <Check className="w-3 h-3 text-green-500" />
          ) : (
            <Copy className="w-3 h-3" />
          )}
        </button>
      </div>
    </div>
  )
}

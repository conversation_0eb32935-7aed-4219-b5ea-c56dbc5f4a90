"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/news-card.tsx":
/*!**********************************!*\
  !*** ./components/news-card.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewsCard: () => (/* binding */ NewsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _share_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./share-button */ \"(app-pages-browser)/./components/share-button.tsx\");\n/* __next_internal_client_entry_do_not_use__ NewsCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NewsCard(param) {\n    let { news } = param;\n    _s();\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likes, setLikes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(news.likes);\n    const handleLike = async ()=>{\n        try {\n            const response = await fetch(\"/api/news/\".concat(news.id, \"/like\"), {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                setIsLiked(!isLiked);\n                setLikes((prev)=>isLiked ? prev - 1 : prev + 1);\n            }\n        } catch (error) {\n            console.error(\"Failed to like news:\", error);\n            // Optimistic update for demo\n            setIsLiked(!isLiked);\n            setLikes((prev)=>isLiked ? prev - 1 : prev + 1);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return \"Just now\";\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        return date.toLocaleDateString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group relative w-full cursor-pointer perspective-1000\",\n        onClick: ()=>setIsFlipped(!isFlipped),\n        id: \"news-card-\".concat(news.id),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full transition-transform duration-700 transform-style-preserve-3d \".concat(isFlipped ? \"rotate-y-180\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 w-full backface-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-gradient-to-r from-red-500 to-red-600 px-4 py-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1 left-0 right-0 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                ...Array(8)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-300 text-xs\",\n                                                    children: \"⭐\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: news.author.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-lg font-bold\",\n                                                children: \"Flip News\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-48 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: news.image,\n                                    alt: news.title,\n                                    fill: true,\n                                    className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gradient-to-b from-white to-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-gray-900 mb-3 leading-tight\",\n                                        children: news.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 text-sm leading-relaxed mb-4 line-clamp-4\",\n                                        children: news.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 bottom-0 w-20 h-20 opacity-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full rounded-full border-4 border-gray-300 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-xs font-bold\",\n                                                        children: \"FLIP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_share_button__WEBPACK_IMPORTED_MODULE_4__.ShareButton, {\n                                                    news: news,\n                                                    onShare: (e)=>e.stopPropagation()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 w-full h-full backface-hidden rotate-y-180\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg shadow-md h-full flex flex-col justify-center items-center text-white p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold mb-4 text-center\",\n                                children: news.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-100 text-sm text-center mb-6 leading-relaxed\",\n                                children: news.excerpt\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/news/\".concat(news.id),\n                                className: \"bg-white text-blue-600 px-6 py-2 rounded-full font-medium hover:bg-blue-50 transition-colors\",\n                                onClick: (e)=>e.stopPropagation(),\n                                children: \"Read Full Story\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-card.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsCard, \"0KF6zs3jxVXe7PaRoExOy21BoJk=\");\n_c = NewsCard;\nvar _c;\n$RefreshReg$(_c, \"NewsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/news-card.tsx\n"));

/***/ })

});
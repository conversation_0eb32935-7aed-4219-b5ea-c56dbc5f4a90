/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/openid-client";
exports.ids = ["vendor-chunks/openid-client"];
exports.modules = {

/***/ "(rsc)/./node_modules/openid-client/lib/client.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/client.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nconst { inspect } = __webpack_require__(/*! util */ \"util\");\nconst stdhttp = __webpack_require__(/*! http */ \"http\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { URL, URLSearchParams } = __webpack_require__(/*! url */ \"url\");\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst tokenHash = __webpack_require__(/*! oidc-token-hash */ \"(rsc)/./node_modules/oidc-token-hash/lib/index.js\");\nconst isKeyObject = __webpack_require__(/*! ./helpers/is_key_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\");\nconst decodeJWT = __webpack_require__(/*! ./helpers/decode_jwt */ \"(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\");\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst defaults = __webpack_require__(/*! ./helpers/defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./helpers/www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = __webpack_require__(/*! ./helpers/assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst isPlainObject = __webpack_require__(/*! ./helpers/is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst { random } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { CLOCK_TOLERANCE } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst { keystores } = __webpack_require__(/*! ./helpers/weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst KeyStore = __webpack_require__(/*! ./helpers/keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nconst { queryKeyStore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst DeviceFlowHandle = __webpack_require__(/*! ./device_flow_handle */ \"(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\");\nconst [major, minor] = process.version.slice(1).split('.').map((str)=>parseInt(str, 10));\nconst rsaPssParams = major >= 17 || major === 16 && minor >= 9;\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\nfunction pickCb(input) {\n    return pick(input, 'access_token', 'code', 'error_description', 'error_uri', 'error', 'expires_in', 'id_token', 'iss', 'response', 'session_state', 'state', 'token_type');\n}\nfunction authorizationHeaderValue(token, tokenType = 'Bearer') {\n    return `${tokenType} ${token}`;\n}\nfunction getSearchParams(input) {\n    const parsed = url.parse(input);\n    if (!parsed.search) return {};\n    return querystring.parse(parsed.search.substring(1));\n}\nfunction verifyPresence(payload, jwt, prop) {\n    if (payload[prop] === undefined) {\n        throw new RPError({\n            message: `missing required JWT property ${prop}`,\n            jwt\n        });\n    }\n}\nfunction authorizationParams(params) {\n    const authParams = {\n        client_id: this.client_id,\n        scope: 'openid',\n        response_type: resolveResponseType.call(this),\n        redirect_uri: resolveRedirectUri.call(this),\n        ...params\n    };\n    Object.entries(authParams).forEach(([key, value])=>{\n        if (value === null || value === undefined) {\n            delete authParams[key];\n        } else if (key === 'claims' && typeof value === 'object') {\n            authParams[key] = JSON.stringify(value);\n        } else if (key === 'resource' && Array.isArray(value)) {\n            authParams[key] = value;\n        } else if (typeof value !== 'string') {\n            authParams[key] = String(value);\n        }\n    });\n    return authParams;\n}\nfunction getKeystore(jwks) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !('kty' in k))) {\n        throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n    return KeyStore.fromJWKS(jwks, {\n        onlyPrivate: true\n    });\n}\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n    try {\n        const supported = client.issuer.token_endpoint_auth_methods_supported;\n        if (!supported.includes(properties.token_endpoint_auth_method)) {\n            if (supported.includes('client_secret_post')) {\n                properties.token_endpoint_auth_method = 'client_secret_post';\n            }\n        }\n    } catch (err) {}\n}\nfunction handleCommonMistakes(client, metadata, properties) {\n    if (!metadata.token_endpoint_auth_method) {\n        // if no explicit value was provided\n        checkBasicSupport(client, properties);\n    }\n    // :fp: c'mon people... RTFM\n    if (metadata.redirect_uri) {\n        if (metadata.redirect_uris) {\n            throw new TypeError('provide a redirect_uri or redirect_uris, not both');\n        }\n        properties.redirect_uris = [\n            metadata.redirect_uri\n        ];\n        delete properties.redirect_uri;\n    }\n    if (metadata.response_type) {\n        if (metadata.response_types) {\n            throw new TypeError('provide a response_type or response_types, not both');\n        }\n        properties.response_types = [\n            metadata.response_type\n        ];\n        delete properties.response_type;\n    }\n}\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n    const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    if (properties[eam] === undefined && properties[easa] === undefined) {\n        if (tokenEndpointAuthMethod !== undefined) {\n            properties[eam] = tokenEndpointAuthMethod;\n        }\n        if (tokenEndpointAuthSigningAlg !== undefined) {\n            properties[easa] = tokenEndpointAuthSigningAlg;\n        }\n    }\n}\nclass BaseClient {\n    #metadata;\n    #issuer;\n    #aadIssValidation;\n    #additionalAuthorizedParties;\n    constructor(issuer, aadIssValidation, metadata = {}, jwks, options){\n        this.#metadata = new Map();\n        this.#issuer = issuer;\n        this.#aadIssValidation = aadIssValidation;\n        if (typeof metadata.client_id !== 'string' || !metadata.client_id) {\n            throw new TypeError('client_id is required');\n        }\n        const properties = {\n            grant_types: [\n                'authorization_code'\n            ],\n            id_token_signed_response_alg: 'RS256',\n            authorization_signed_response_alg: 'RS256',\n            response_types: [\n                'code'\n            ],\n            token_endpoint_auth_method: 'client_secret_basic',\n            ...this.fapi1() ? {\n                grant_types: [\n                    'authorization_code',\n                    'implicit'\n                ],\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                response_types: [\n                    'code id_token'\n                ],\n                tls_client_certificate_bound_access_tokens: true,\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...this.fapi2() ? {\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...metadata\n        };\n        if (this.fapi()) {\n            switch(properties.token_endpoint_auth_method){\n                case 'self_signed_tls_client_auth':\n                case 'tls_client_auth':\n                    break;\n                case 'private_key_jwt':\n                    if (!jwks) {\n                        throw new TypeError('jwks is required');\n                    }\n                    break;\n                case undefined:\n                    throw new TypeError('token_endpoint_auth_method is required');\n                default:\n                    throw new TypeError('invalid or unsupported token_endpoint_auth_method');\n            }\n        }\n        if (this.fapi2()) {\n            if (properties.tls_client_certificate_bound_access_tokens && properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n            if (!properties.tls_client_certificate_bound_access_tokens && !properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n        }\n        handleCommonMistakes(this, metadata, properties);\n        assertSigningAlgValuesSupport('token', this.issuer, properties);\n        [\n            'introspection',\n            'revocation'\n        ].forEach((endpoint)=>{\n            getDefaultsForEndpoint(endpoint, this.issuer, properties);\n            assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n        });\n        Object.entries(properties).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        if (jwks !== undefined) {\n            const keystore = getKeystore.call(this, jwks);\n            keystores.set(this, keystore);\n        }\n        if (options != null && options.additionalAuthorizedParties) {\n            this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n        }\n        this[CLOCK_TOLERANCE] = 0;\n    }\n    authorizationUrl(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        assertIssuerConfiguration(this.issuer, 'authorization_endpoint');\n        const target = new URL(this.issuer.authorization_endpoint);\n        for (const [name, value] of Object.entries(authorizationParams.call(this, params))){\n            if (Array.isArray(value)) {\n                target.searchParams.delete(name);\n                for (const member of value){\n                    target.searchParams.append(name, member);\n                }\n            } else {\n                target.searchParams.set(name, value);\n            }\n        }\n        // TODO: is the replace needed?\n        return target.href.replace(/\\+/g, '%20');\n    }\n    authorizationPost(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        const inputs = authorizationParams.call(this, params);\n        const formInputs = Object.keys(inputs).map((name)=>`<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`).join('\\n');\n        return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n    }\n    endSessionUrl(params = {}) {\n        assertIssuerConfiguration(this.issuer, 'end_session_endpoint');\n        const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n        const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n        let id_token_hint;\n        ({ id_token_hint, ...params } = params);\n        if (id_token_hint instanceof TokenSet) {\n            if (!id_token_hint.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            id_token_hint = id_token_hint.id_token;\n        }\n        const target = url.parse(this.issuer.end_session_endpoint);\n        const query = defaults(getSearchParams(this.issuer.end_session_endpoint), params, {\n            post_logout_redirect_uri,\n            client_id: this.client_id\n        }, {\n            id_token_hint\n        });\n        Object.entries(query).forEach(([key, value])=>{\n            if (value === null || value === undefined) {\n                delete query[key];\n            }\n        });\n        target.search = null;\n        target.query = query;\n        return url.format(target);\n    }\n    callbackParams(input) {\n        const isIncomingMessage = input instanceof stdhttp.IncomingMessage || input && input.method && input.url;\n        const isString = typeof input === 'string';\n        if (!isString && !isIncomingMessage) {\n            throw new TypeError('#callbackParams only accepts string urls, http.IncomingMessage or a lookalike');\n        }\n        if (isIncomingMessage) {\n            switch(input.method){\n                case 'GET':\n                    return pickCb(getSearchParams(input.url));\n                case 'POST':\n                    if (input.body === undefined) {\n                        throw new TypeError('incoming message body missing, include a body parser prior to this method call');\n                    }\n                    switch(typeof input.body){\n                        case 'object':\n                        case 'string':\n                            if (Buffer.isBuffer(input.body)) {\n                                return pickCb(querystring.parse(input.body.toString('utf-8')));\n                            }\n                            if (typeof input.body === 'string') {\n                                return pickCb(querystring.parse(input.body));\n                            }\n                            return pickCb(input.body);\n                        default:\n                            throw new TypeError('invalid IncomingMessage body object');\n                    }\n                default:\n                    throw new TypeError('invalid IncomingMessage method');\n            }\n        } else {\n            return pickCb(getSearchParams(input));\n        }\n    }\n    async callback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (this.default_max_age && !checks.max_age) {\n            checks.max_age = this.default_max_age;\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            id_token: [\n                'id_token'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                } else {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.id_token) {\n            const tokenset = new TokenSet(params);\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'authorization', checks.max_age, checks.state);\n            if (!params.code) {\n                return tokenset;\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'token', checks.max_age);\n            if (params.session_state) {\n                tokenset.session_state = params.session_state;\n            }\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async oauthCallback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        if (typeof params.id_token === 'string' && params.id_token.length) {\n            throw new RPError({\n                message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                params\n            });\n        }\n        delete params.id_token;\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                }\n                if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            if (typeof tokenset.id_token === 'string' && tokenset.id_token.length) {\n                throw new RPError({\n                    message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                    params\n                });\n            }\n            delete tokenset.id_token;\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async decryptIdToken(token) {\n        if (!this.id_token_encrypted_response_alg) {\n            return token;\n        }\n        let idToken = token;\n        if (idToken instanceof TokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        const expectedAlg = this.id_token_encrypted_response_alg;\n        const expectedEnc = this.id_token_encrypted_response_enc;\n        const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n        if (token instanceof TokenSet) {\n            token.id_token = result;\n            return token;\n        }\n        return result;\n    }\n    async validateJWTUserinfo(body) {\n        const expectedAlg = this.userinfo_signed_response_alg;\n        return this.validateJWT(body, expectedAlg, []);\n    }\n    async decryptJARM(response) {\n        if (!this.authorization_encrypted_response_alg) {\n            return response;\n        }\n        const expectedAlg = this.authorization_encrypted_response_alg;\n        const expectedEnc = this.authorization_encrypted_response_enc;\n        return this.decryptJWE(response, expectedAlg, expectedEnc);\n    }\n    async decryptJWTUserinfo(body) {\n        if (!this.userinfo_encrypted_response_alg) {\n            return body;\n        }\n        const expectedAlg = this.userinfo_encrypted_response_alg;\n        const expectedEnc = this.userinfo_encrypted_response_enc;\n        return this.decryptJWE(body, expectedAlg, expectedEnc);\n    }\n    async decryptJWE(jwe, expectedAlg, expectedEnc = 'A128CBC-HS256') {\n        const header = JSON.parse(base64url.decode(jwe.split('.')[0]));\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt: jwe\n            });\n        }\n        if (header.enc !== expectedEnc) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE enc received, expected %s, got: %s',\n                    expectedEnc,\n                    header.enc\n                ],\n                jwt: jwe\n            });\n        }\n        const getPlaintext = (result)=>new TextDecoder().decode(result.plaintext);\n        let plaintext;\n        if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n            const keystore = await keystores.get(this);\n            const protectedHeader = jose.decodeProtectedHeader(jwe);\n            for (const key of keystore.all({\n                ...protectedHeader,\n                use: 'enc'\n            })){\n                plaintext = await jose.compactDecrypt(jwe, await key.keyObject(protectedHeader.alg)).then(getPlaintext, ()=>{});\n                if (plaintext) break;\n            }\n        } else {\n            plaintext = await jose.compactDecrypt(jwe, this.secretForAlg(expectedAlg === 'dir' ? expectedEnc : expectedAlg)).then(getPlaintext, ()=>{});\n        }\n        if (!plaintext) {\n            throw new RPError({\n                message: 'failed to decrypt JWE',\n                jwt: jwe\n            });\n        }\n        return plaintext;\n    }\n    async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n        let idToken = tokenSet;\n        const expectedAlg = this.id_token_signed_response_alg;\n        const isTokenSet = idToken instanceof TokenSet;\n        if (isTokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        idToken = String(idToken);\n        const timestamp = now();\n        const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n        if (typeof maxAge === 'number' || maxAge !== skipMaxAgeCheck && this.require_auth_time) {\n            if (!payload.auth_time) {\n                throw new RPError({\n                    message: 'missing required JWT property auth_time',\n                    jwt: idToken\n                });\n            }\n            if (typeof payload.auth_time !== 'number') {\n                throw new RPError({\n                    message: 'JWT auth_time claim must be a JSON numeric value',\n                    jwt: idToken\n                });\n            }\n        }\n        if (typeof maxAge === 'number' && payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]) {\n            throw new RPError({\n                printf: [\n                    'too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i',\n                    maxAge,\n                    payload.auth_time,\n                    timestamp - this[CLOCK_TOLERANCE]\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                auth_time: payload.auth_time,\n                jwt: idToken\n            });\n        }\n        if (nonce !== skipNonceCheck && (payload.nonce || nonce !== undefined) && payload.nonce !== nonce) {\n            throw new RPError({\n                printf: [\n                    'nonce mismatch, expected %s, got: %s',\n                    nonce,\n                    payload.nonce\n                ],\n                jwt: idToken\n            });\n        }\n        if (returnedBy === 'authorization') {\n            if (!payload.at_hash && tokenSet.access_token) {\n                throw new RPError({\n                    message: 'missing required property at_hash',\n                    jwt: idToken\n                });\n            }\n            if (!payload.c_hash && tokenSet.code) {\n                throw new RPError({\n                    message: 'missing required property c_hash',\n                    jwt: idToken\n                });\n            }\n            if (this.fapi1()) {\n                if (!payload.s_hash && (tokenSet.state || state)) {\n                    throw new RPError({\n                        message: 'missing required property s_hash',\n                        jwt: idToken\n                    });\n                }\n            }\n            if (payload.s_hash) {\n                if (!state) {\n                    throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n                }\n                try {\n                    tokenHash.validate({\n                        claim: 's_hash',\n                        source: 'state'\n                    }, payload.s_hash, state, header.alg, key.jwk && key.jwk.crv);\n                } catch (err) {\n                    throw new RPError({\n                        message: err.message,\n                        jwt: idToken\n                    });\n                }\n            }\n        }\n        if (this.fapi() && payload.iat < timestamp - 3600) {\n            throw new RPError({\n                printf: [\n                    'JWT issued too far in the past, now %i, iat %i',\n                    timestamp,\n                    payload.iat\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                iat: payload.iat,\n                jwt: idToken\n            });\n        }\n        if (tokenSet.access_token && payload.at_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'at_hash',\n                    source: 'access_token'\n                }, payload.at_hash, tokenSet.access_token, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        if (tokenSet.code && payload.c_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'c_hash',\n                    source: 'code'\n                }, payload.c_hash, tokenSet.code, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        return tokenSet;\n    }\n    async validateJWT(jwt, expectedAlg, required = [\n        'iss',\n        'sub',\n        'aud',\n        'exp',\n        'iat'\n    ]) {\n        const isSelfIssued = this.issuer.issuer === 'https://self-issued.me';\n        const timestamp = now();\n        let header;\n        let payload;\n        try {\n            ({ header, payload } = decodeJWT(jwt, {\n                complete: true\n            }));\n        } catch (err) {\n            throw new RPError({\n                printf: [\n                    'failed to decode JWT (%s: %s)',\n                    err.name,\n                    err.message\n                ],\n                jwt\n            });\n        }\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWT alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt\n            });\n        }\n        if (isSelfIssued) {\n            required = [\n                ...required,\n                'sub_jwk'\n            ];\n        }\n        required.forEach(verifyPresence.bind(undefined, payload, jwt));\n        if (payload.iss !== undefined) {\n            let expectedIss = this.issuer.issuer;\n            if (this.#aadIssValidation) {\n                expectedIss = this.issuer.issuer.replace('{tenantid}', payload.tid);\n            }\n            if (payload.iss !== expectedIss) {\n                throw new RPError({\n                    printf: [\n                        'unexpected iss value, expected %s, got: %s',\n                        expectedIss,\n                        payload.iss\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.iat !== undefined) {\n            if (typeof payload.iat !== 'number') {\n                throw new RPError({\n                    message: 'JWT iat claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n        }\n        if (payload.nbf !== undefined) {\n            if (typeof payload.nbf !== 'number') {\n                throw new RPError({\n                    message: 'JWT nbf claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n                throw new RPError({\n                    printf: [\n                        'JWT not active yet, now %i, nbf %i',\n                        timestamp + this[CLOCK_TOLERANCE],\n                        payload.nbf\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    nbf: payload.nbf,\n                    jwt\n                });\n            }\n        }\n        if (payload.exp !== undefined) {\n            if (typeof payload.exp !== 'number') {\n                throw new RPError({\n                    message: 'JWT exp claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n                throw new RPError({\n                    printf: [\n                        'JWT expired, now %i, exp %i',\n                        timestamp - this[CLOCK_TOLERANCE],\n                        payload.exp\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    exp: payload.exp,\n                    jwt\n                });\n            }\n        }\n        if (payload.aud !== undefined) {\n            if (Array.isArray(payload.aud)) {\n                if (payload.aud.length > 1 && !payload.azp) {\n                    throw new RPError({\n                        message: 'missing required JWT property azp',\n                        jwt\n                    });\n                }\n                if (!payload.aud.includes(this.client_id)) {\n                    throw new RPError({\n                        printf: [\n                            'aud is missing the client_id, expected %s to be included in %j',\n                            this.client_id,\n                            payload.aud\n                        ],\n                        jwt\n                    });\n                }\n            } else if (payload.aud !== this.client_id) {\n                throw new RPError({\n                    printf: [\n                        'aud mismatch, expected %s, got: %s',\n                        this.client_id,\n                        payload.aud\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.azp !== undefined) {\n            let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n            if (typeof additionalAuthorizedParties === 'string') {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    additionalAuthorizedParties\n                ];\n            } else if (Array.isArray(additionalAuthorizedParties)) {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    ...additionalAuthorizedParties\n                ];\n            } else {\n                additionalAuthorizedParties = [\n                    this.client_id\n                ];\n            }\n            if (!additionalAuthorizedParties.includes(payload.azp)) {\n                throw new RPError({\n                    printf: [\n                        'azp mismatch, got: %s',\n                        payload.azp\n                    ],\n                    jwt\n                });\n            }\n        }\n        let keys;\n        if (isSelfIssued) {\n            try {\n                assert(isPlainObject(payload.sub_jwk));\n                const key = await jose.importJWK(payload.sub_jwk, header.alg);\n                assert.equal(key.type, 'public');\n                keys = [\n                    {\n                        keyObject () {\n                            return key;\n                        }\n                    }\n                ];\n            } catch (err) {\n                throw new RPError({\n                    message: 'failed to use sub_jwk claim as an asymmetric JSON Web Key',\n                    jwt\n                });\n            }\n            if (await jose.calculateJwkThumbprint(payload.sub_jwk) !== payload.sub) {\n                throw new RPError({\n                    message: 'failed to match the subject with sub_jwk',\n                    jwt\n                });\n            }\n        } else if (header.alg.startsWith('HS')) {\n            keys = [\n                this.secretForAlg(header.alg)\n            ];\n        } else if (header.alg !== 'none') {\n            keys = await queryKeyStore.call(this.issuer, {\n                ...header,\n                use: 'sig'\n            });\n        }\n        if (!keys && header.alg === 'none') {\n            return {\n                protected: header,\n                payload\n            };\n        }\n        for (const key of keys){\n            const verified = await jose.compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg)).catch(()=>{});\n            if (verified) {\n                return {\n                    payload,\n                    protected: verified.protectedHeader,\n                    key\n                };\n            }\n        }\n        throw new RPError({\n            message: 'failed to validate JWT signature',\n            jwt\n        });\n    }\n    async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let token = refreshToken;\n        if (token instanceof TokenSet) {\n            if (!token.refresh_token) {\n                throw new TypeError('refresh_token not present in TokenSet');\n            }\n            token = token.refresh_token;\n        }\n        const tokenset = await this.grant({\n            ...exchangeBody,\n            grant_type: 'refresh_token',\n            refresh_token: String(token)\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        if (tokenset.id_token) {\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, skipNonceCheck, 'token', skipMaxAgeCheck);\n            if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n                const expectedSub = refreshToken.claims().sub;\n                const actualSub = tokenset.claims().sub;\n                if (actualSub !== expectedSub) {\n                    throw new RPError({\n                        printf: [\n                            'sub mismatch, expected %s, got: %s',\n                            expectedSub,\n                            actualSub\n                        ],\n                        jwt: tokenset.id_token\n                    });\n                }\n            }\n        }\n        return tokenset;\n    }\n    async requestResource(resourceUrl, accessToken, { method, headers, body, DPoP, tokenType = DPoP ? 'DPoP' : accessToken instanceof TokenSet ? accessToken.token_type : 'Bearer' } = {}, retry) {\n        if (accessToken instanceof TokenSet) {\n            if (!accessToken.access_token) {\n                throw new TypeError('access_token not present in TokenSet');\n            }\n            accessToken = accessToken.access_token;\n        }\n        if (!accessToken) {\n            throw new TypeError('no access token provided');\n        } else if (typeof accessToken !== 'string') {\n            throw new TypeError('invalid access token provided');\n        }\n        const requestOpts = {\n            headers: {\n                Authorization: authorizationHeaderValue(accessToken, tokenType),\n                ...headers\n            },\n            body\n        };\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        const response = await request.call(this, {\n            ...requestOpts,\n            responseType: 'buffer',\n            method,\n            url: resourceUrl\n        }, {\n            accessToken,\n            mTLS,\n            DPoP\n        });\n        const wwwAuthenticate = response.headers['www-authenticate'];\n        if (retry !== retryAttempt && wwwAuthenticate && wwwAuthenticate.toLowerCase().startsWith('dpop ') && parseWwwAuthenticate(wwwAuthenticate).error === 'use_dpop_nonce') {\n            return this.requestResource(resourceUrl, accessToken, {\n                method,\n                headers,\n                body,\n                DPoP,\n                tokenType\n            });\n        }\n        return response;\n    }\n    async userinfo(accessToken, { method = 'GET', via = 'header', tokenType, params, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'userinfo_endpoint');\n        const options = {\n            tokenType,\n            method: String(method).toUpperCase(),\n            DPoP\n        };\n        if (options.method !== 'GET' && options.method !== 'POST') {\n            throw new TypeError('#userinfo() method can only be POST or a GET');\n        }\n        if (via === 'body' && options.method !== 'POST') {\n            throw new TypeError('can only send body on POST');\n        }\n        const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n        if (jwt) {\n            options.headers = {\n                Accept: 'application/jwt'\n            };\n        } else {\n            options.headers = {\n                Accept: 'application/json'\n            };\n        }\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        let targetUrl;\n        if (mTLS && this.issuer.mtls_endpoint_aliases) {\n            targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n        }\n        targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n        if (via === 'body') {\n            options.headers.Authorization = undefined;\n            options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n            options.body = new URLSearchParams();\n            options.body.append('access_token', accessToken instanceof TokenSet ? accessToken.access_token : accessToken);\n        }\n        // handle additional parameters, GET via querystring, POST via urlencoded body\n        if (params) {\n            if (options.method === 'GET') {\n                Object.entries(params).forEach(([key, value])=>{\n                    targetUrl.searchParams.append(key, value);\n                });\n            } else if (options.body) {\n                // POST && via body\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            } else {\n                // POST && via header\n                options.body = new URLSearchParams();\n                options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            }\n        }\n        if (options.body) {\n            options.body = options.body.toString();\n        }\n        const response = await this.requestResource(targetUrl, accessToken, options);\n        let parsed = processResponse(response, {\n            bearer: true\n        });\n        if (jwt) {\n            if (!/^application\\/jwt/.test(response.headers['content-type'])) {\n                throw new RPError({\n                    message: 'expected application/jwt response from the userinfo_endpoint',\n                    response\n                });\n            }\n            const body = response.body.toString();\n            const userinfo = await this.decryptJWTUserinfo(body);\n            if (!this.userinfo_signed_response_alg) {\n                try {\n                    parsed = JSON.parse(userinfo);\n                    assert(isPlainObject(parsed));\n                } catch (err) {\n                    throw new RPError({\n                        message: 'failed to parse userinfo JWE payload as JSON',\n                        jwt: userinfo\n                    });\n                }\n            } else {\n                ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n            }\n        } else {\n            try {\n                parsed = JSON.parse(response.body);\n            } catch (err) {\n                Object.defineProperty(err, 'response', {\n                    value: response\n                });\n                throw err;\n            }\n        }\n        if (accessToken instanceof TokenSet && accessToken.id_token) {\n            const expectedSub = accessToken.claims().sub;\n            if (parsed.sub !== expectedSub) {\n                throw new RPError({\n                    printf: [\n                        'userinfo sub mismatch, expected %s, got: %s',\n                        expectedSub,\n                        parsed.sub\n                    ],\n                    body: parsed,\n                    jwt: accessToken.id_token\n                });\n            }\n        }\n        return parsed;\n    }\n    encryptionSecret(len) {\n        const hash = len <= 256 ? 'sha256' : len <= 384 ? 'sha384' : len <= 512 ? 'sha512' : false;\n        if (!hash) {\n            throw new Error('unsupported symmetric encryption key derivation');\n        }\n        return crypto.createHash(hash).update(this.client_secret).digest().slice(0, len / 8);\n    }\n    secretForAlg(alg) {\n        if (!this.client_secret) {\n            throw new TypeError('client_secret is required');\n        }\n        if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$1, 10));\n        }\n        if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n        }\n        return new TextEncoder().encode(this.client_secret);\n    }\n    async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const response = await authenticatedPost.call(this, 'token', {\n            form: body,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        let responseBody;\n        try {\n            responseBody = processResponse(response);\n        } catch (err) {\n            if (retry !== retryAttempt && err instanceof OPError && err.error === 'use_dpop_nonce') {\n                return this.grant(body, {\n                    clientAssertionPayload,\n                    DPoP\n                }, retryAttempt);\n            }\n            throw err;\n        }\n        return new TokenSet(responseBody);\n    }\n    async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'device_authorization_endpoint');\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const body = authorizationParams.call(this, {\n            client_id: this.client_id,\n            redirect_uri: null,\n            response_type: null,\n            ...params\n        });\n        const response = await authenticatedPost.call(this, 'device_authorization', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response);\n        return new DeviceFlowHandle({\n            client: this,\n            exchangeBody,\n            clientAssertionPayload,\n            response: responseBody,\n            maxAge: params.max_age,\n            DPoP\n        });\n    }\n    async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'revocation_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...revokeBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'revocation', {\n            form\n        }, {\n            clientAssertionPayload\n        });\n        processResponse(response, {\n            body: false\n        });\n    }\n    async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'introspection_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...introspectBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'introspection', {\n            form,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload\n        });\n        const responseBody = processResponse(response);\n        return responseBody;\n    }\n    static async register(metadata, options = {}) {\n        const { initialAccessToken, jwks, ...clientOptions } = options;\n        assertIssuerConfiguration(this.issuer, 'registration_endpoint');\n        if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n            const keystore = await getKeystore.call(this, jwks);\n            metadata.jwks = keystore.toJWKS();\n        }\n        const response = await request.call(this, {\n            headers: {\n                Accept: 'application/json',\n                ...initialAccessToken ? {\n                    Authorization: authorizationHeaderValue(initialAccessToken)\n                } : undefined\n            },\n            responseType: 'json',\n            json: metadata,\n            url: this.issuer.registration_endpoint,\n            method: 'POST'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201,\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n        const response = await request.call(this, {\n            method: 'GET',\n            url: registrationClientUri,\n            responseType: 'json',\n            headers: {\n                Authorization: authorizationHeaderValue(registrationAccessToken),\n                Accept: 'application/json'\n            }\n        });\n        const responseBody = processResponse(response, {\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    async requestObject(requestObject = {}, { sign: signingAlgorithm = this.request_object_signing_alg || 'none', encrypt: { alg: eKeyManagement = this.request_object_encryption_alg, enc: eContentEncryption = this.request_object_encryption_enc || 'A128CBC-HS256' } = {} } = {}) {\n        if (!isPlainObject(requestObject)) {\n            throw new TypeError('requestObject must be a plain object');\n        }\n        let signed;\n        let key;\n        const unix = now();\n        const header = {\n            alg: signingAlgorithm,\n            typ: 'oauth-authz-req+jwt'\n        };\n        const payload = JSON.stringify(defaults({}, requestObject, {\n            iss: this.client_id,\n            aud: this.issuer.issuer,\n            client_id: this.client_id,\n            jti: random(),\n            iat: unix,\n            exp: unix + 300,\n            ...this.fapi() ? {\n                nbf: unix\n            } : undefined\n        }));\n        if (signingAlgorithm === 'none') {\n            signed = [\n                base64url.encode(JSON.stringify(header)),\n                base64url.encode(payload),\n                ''\n            ].join('.');\n        } else {\n            const symmetric = signingAlgorithm.startsWith('HS');\n            if (symmetric) {\n                key = this.secretForAlg(signingAlgorithm);\n            } else {\n                const keystore = await keystores.get(this);\n                if (!keystore) {\n                    throw new TypeError(`no keystore present for client, cannot sign using alg ${signingAlgorithm}`);\n                }\n                key = keystore.get({\n                    alg: signingAlgorithm,\n                    use: 'sig'\n                });\n                if (!key) {\n                    throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n                }\n            }\n            signed = await new jose.CompactSign(new TextEncoder().encode(payload)).setProtectedHeader({\n                ...header,\n                kid: symmetric ? undefined : key.jwk.kid\n            }).sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n        }\n        if (!eKeyManagement) {\n            return signed;\n        }\n        const fields = {\n            alg: eKeyManagement,\n            enc: eContentEncryption,\n            cty: 'oauth-authz-req+jwt'\n        };\n        if (fields.alg.match(/^(RSA|ECDH)/)) {\n            [key] = await queryKeyStore.call(this.issuer, {\n                alg: fields.alg,\n                use: 'enc'\n            }, {\n                allowMulti: true\n            });\n        } else {\n            key = this.secretForAlg(fields.alg === 'dir' ? fields.enc : fields.alg);\n        }\n        return new jose.CompactEncrypt(new TextEncoder().encode(signed)).setProtectedHeader({\n            ...fields,\n            kid: key instanceof Uint8Array ? undefined : key.jwk.kid\n        }).encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n    }\n    async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'pushed_authorization_request_endpoint');\n        const body = {\n            ...'request' in params ? params : authorizationParams.call(this, params),\n            client_id: this.client_id\n        };\n        const response = await authenticatedPost.call(this, 'pushed_authorization_request', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201\n        });\n        if (!('expires_in' in responseBody)) {\n            throw new RPError({\n                message: 'expected expires_in in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.expires_in !== 'number') {\n            throw new RPError({\n                message: 'invalid expires_in value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (!('request_uri' in responseBody)) {\n            throw new RPError({\n                message: 'expected request_uri in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.request_uri !== 'string') {\n            throw new RPError({\n                message: 'invalid request_uri value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        return responseBody;\n    }\n    get issuer() {\n        return this.#issuer;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n    fapi() {\n        return this.fapi1() || this.fapi2();\n    }\n    fapi1() {\n        return this.constructor.name === 'FAPI1Client';\n    }\n    fapi2() {\n        return this.constructor.name === 'FAPI2Client';\n    }\n    async validateJARM(response) {\n        const expectedAlg = this.authorization_signed_response_alg;\n        const { payload } = await this.validateJWT(response, expectedAlg, [\n            'iss',\n            'exp',\n            'aud'\n        ]);\n        return pickCb(payload);\n    }\n    /**\n   * @name dpopProof\n   * @api private\n   */ async dpopProof(payload, privateKeyInput, accessToken) {\n        if (!isPlainObject(payload)) {\n            throw new TypeError('payload must be a plain object');\n        }\n        let privateKey;\n        if (isKeyObject(privateKeyInput)) {\n            privateKey = privateKeyInput;\n        } else if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            privateKey = privateKeyInput;\n        } else if (jose.cryptoRuntime === 'node:crypto') {\n            privateKey = crypto.createPrivateKey(privateKeyInput);\n        } else {\n            throw new TypeError('unrecognized crypto runtime');\n        }\n        if (privateKey.type !== 'private') {\n            throw new TypeError('\"DPoP\" option must be a private key');\n        }\n        let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n        if (!alg) {\n            throw new TypeError('could not determine DPoP JWS Algorithm');\n        }\n        return new jose.SignJWT({\n            ath: accessToken ? base64url.encode(crypto.createHash('sha256').update(accessToken).digest()) : undefined,\n            ...payload\n        }).setProtectedHeader({\n            alg,\n            typ: 'dpop+jwt',\n            jwk: await getJwk(privateKey, privateKeyInput)\n        }).setIssuedAt().setJti(random()).sign(privateKey);\n    }\n}\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n    switch(cryptoKey.algorithm.name){\n        case 'Ed25519':\n        case 'Ed448':\n            return 'EdDSA';\n        case 'ECDSA':\n            {\n                switch(cryptoKey.algorithm.namedCurve){\n                    case 'P-256':\n                        return 'ES256';\n                    case 'P-384':\n                        return 'ES384';\n                    case 'P-521':\n                        return 'ES512';\n                    default:\n                        break;\n                }\n                break;\n            }\n        case 'RSASSA-PKCS1-v1_5':\n            return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        case 'RSA-PSS':\n            return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        default:\n            throw new TypeError('unsupported DPoP private key');\n    }\n}\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === 'node:crypto') {\n    determineDPoPAlgorithm = function(privateKey, privateKeyInput) {\n        if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            return determineDPoPAlgorithmFromCryptoKey(privateKey);\n        }\n        switch(privateKey.asymmetricKeyType){\n            case 'ed25519':\n            case 'ed448':\n                return 'EdDSA';\n            case 'ec':\n                return determineEcAlgorithm(privateKey, privateKeyInput);\n            case 'rsa':\n            case rsaPssParams && 'rsa-pss':\n                return determineRsaAlgorithm(privateKey, privateKeyInput, this.issuer.dpop_signing_alg_values_supported);\n            default:\n                throw new TypeError('unsupported DPoP private key');\n        }\n    };\n    const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n    function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n        if (typeof privateKeyInput === 'object' && privateKeyInput.format === 'jwk' && privateKeyInput.key && privateKeyInput.key.alg) {\n            return privateKeyInput.key.alg;\n        }\n        if (Array.isArray(valuesSupported)) {\n            let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n            if (privateKey.asymmetricKeyType === 'rsa-pss') {\n                candidates = candidates.filter((value)=>value.startsWith('PS'));\n            }\n            return [\n                'PS256',\n                'PS384',\n                'PS512',\n                'RS256',\n                'RS384',\n                'RS384'\n            ].find((preferred)=>candidates.includes(preferred));\n        }\n        return 'PS256';\n    }\n    const p256 = Buffer.from([\n        42,\n        134,\n        72,\n        206,\n        61,\n        3,\n        1,\n        7\n    ]);\n    const p384 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        34\n    ]);\n    const p521 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        35\n    ]);\n    const secp256k1 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        10\n    ]);\n    function determineEcAlgorithm(privateKey, privateKeyInput) {\n        // If input was a JWK\n        switch(typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.key.crv){\n            case 'P-256':\n                return 'ES256';\n            case 'secp256k1':\n                return 'ES256K';\n            case 'P-384':\n                return 'ES384';\n            case 'P-512':\n                return 'ES512';\n            default:\n                break;\n        }\n        const buf = privateKey.export({\n            format: 'der',\n            type: 'pkcs8'\n        });\n        const i = buf[1] < 128 ? 17 : 18;\n        const len = buf[i];\n        const curveOid = buf.slice(i + 1, i + 1 + len);\n        if (curveOid.equals(p256)) {\n            return 'ES256';\n        }\n        if (curveOid.equals(p384)) {\n            return 'ES384';\n        }\n        if (curveOid.equals(p521)) {\n            return 'ES512';\n        }\n        if (curveOid.equals(secp256k1)) {\n            return 'ES256K';\n        }\n        throw new TypeError('unsupported DPoP private key curve');\n    }\n} else {\n    determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n    if (jose.cryptoRuntime === 'node:crypto' && typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.format === 'jwk') {\n        return pick(privateKeyInput.key, 'kty', 'crv', 'x', 'y', 'e', 'n');\n    }\n    if (jwkCache.has(privateKeyInput)) {\n        return jwkCache.get(privateKeyInput);\n    }\n    const jwk = pick(await jose.exportJWK(keyObject), 'kty', 'crv', 'x', 'y', 'e', 'n');\n    if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === 'WebCryptoAPI') {\n        jwkCache.set(privateKeyInput, jwk);\n    }\n    return jwk;\n}\nmodule.exports = (issuer, aadIssValidation = false)=>class Client extends BaseClient {\n        constructor(...args){\n            super(issuer, aadIssValidation, ...args);\n        }\n        static get issuer() {\n            return issuer;\n        }\n    };\nmodule.exports.BaseClient = BaseClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/device_flow_handle.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/device_flow_handle.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\n\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass DeviceFlowHandle {\n  #aborted;\n  #client;\n  #clientAssertionPayload;\n  #DPoP;\n  #exchangeBody;\n  #expires_at;\n  #interval;\n  #maxAge;\n  #response;\n  constructor({ client, exchangeBody, clientAssertionPayload, response, maxAge, DPoP }) {\n    ['verification_uri', 'user_code', 'device_code'].forEach((prop) => {\n      if (typeof response[prop] !== 'string' || !response[prop]) {\n        throw new RPError(\n          `expected ${prop} string to be returned by Device Authorization Response, got %j`,\n          response[prop],\n        );\n      }\n    });\n\n    if (!Number.isSafeInteger(response.expires_in)) {\n      throw new RPError(\n        'expected expires_in number to be returned by Device Authorization Response, got %j',\n        response.expires_in,\n      );\n    }\n\n    this.#expires_at = now() + response.expires_in;\n    this.#client = client;\n    this.#DPoP = DPoP;\n    this.#maxAge = maxAge;\n    this.#exchangeBody = exchangeBody;\n    this.#clientAssertionPayload = clientAssertionPayload;\n    this.#response = response;\n    this.#interval = response.interval * 1000 || 5000;\n  }\n\n  abort() {\n    this.#aborted = true;\n  }\n\n  async poll({ signal } = {}) {\n    if ((signal && signal.aborted) || this.#aborted) {\n      throw new RPError('polling aborted');\n    }\n\n    if (this.expired()) {\n      throw new RPError(\n        'the device code %j has expired and the device authorization session has concluded',\n        this.device_code,\n      );\n    }\n\n    await new Promise((resolve) => setTimeout(resolve, this.#interval));\n\n    let tokenset;\n    try {\n      tokenset = await this.#client.grant(\n        {\n          ...this.#exchangeBody,\n          grant_type: 'urn:ietf:params:oauth:grant-type:device_code',\n          device_code: this.device_code,\n        },\n        { clientAssertionPayload: this.#clientAssertionPayload, DPoP: this.#DPoP },\n      );\n    } catch (err) {\n      switch (err instanceof OPError && err.error) {\n        case 'slow_down':\n          this.#interval += 5000;\n        case 'authorization_pending':\n          return this.poll({ signal });\n        default:\n          throw err;\n      }\n    }\n\n    if ('id_token' in tokenset) {\n      await this.#client.decryptIdToken(tokenset);\n      await this.#client.validateIdToken(tokenset, undefined, 'token', this.#maxAge);\n    }\n\n    return tokenset;\n  }\n\n  get device_code() {\n    return this.#response.device_code;\n  }\n\n  get user_code() {\n    return this.#response.user_code;\n  }\n\n  get verification_uri() {\n    return this.#response.verification_uri;\n  }\n\n  get verification_uri_complete() {\n    return this.#response.verification_uri_complete;\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.#expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.#response, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nmodule.exports = DeviceFlowHandle;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/errors.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/errors.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { format } = __webpack_require__(/*! util */ \"util\");\n\nclass OPError extends Error {\n  constructor({ error_description, error, error_uri, session_state, state, scope }, response) {\n    super(!error_description ? error : `${error} (${error_description})`);\n\n    Object.assign(\n      this,\n      { error },\n      error_description && { error_description },\n      error_uri && { error_uri },\n      state && { state },\n      scope && { scope },\n      session_state && { session_state },\n    );\n\n    if (response) {\n      Object.defineProperty(this, 'response', {\n        value: response,\n      });\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nclass RPError extends Error {\n  constructor(...args) {\n    if (typeof args[0] === 'string') {\n      super(format(...args));\n    } else {\n      const { message, printf, response, ...rest } = args[0];\n      if (printf) {\n        super(format(...printf));\n      } else {\n        super(message);\n      }\n      Object.assign(this, rest);\n      if (response) {\n        Object.defineProperty(this, 'response', {\n          value: response,\n        });\n      }\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nmodule.exports = {\n  OPError,\n  RPError,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZXJyb3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsU0FBUyxFQUFFLG1CQUFPLENBQUMsa0JBQU07O0FBRWpDO0FBQ0EsZ0JBQWdCLGtFQUFrRTtBQUNsRiwwQ0FBMEMsT0FBTyxHQUFHLGtCQUFrQjs7QUFFdEU7QUFDQTtBQUNBLFFBQVEsT0FBTztBQUNmLDZCQUE2QixtQkFBbUI7QUFDaEQscUJBQXFCLFdBQVc7QUFDaEMsaUJBQWlCLE9BQU87QUFDeEIsaUJBQWlCLE9BQU87QUFDeEIseUJBQXlCLGVBQWU7QUFDeEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLGNBQWMscUNBQXFDO0FBQ25EO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFnYXJhanVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVm9pY2ViaXJkIG5ld3NcXGZsaXAtbmV3c1xcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGVycm9ycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB7IGZvcm1hdCB9ID0gcmVxdWlyZSgndXRpbCcpO1xuXG5jbGFzcyBPUEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb25zdHJ1Y3Rvcih7IGVycm9yX2Rlc2NyaXB0aW9uLCBlcnJvciwgZXJyb3JfdXJpLCBzZXNzaW9uX3N0YXRlLCBzdGF0ZSwgc2NvcGUgfSwgcmVzcG9uc2UpIHtcbiAgICBzdXBlcighZXJyb3JfZGVzY3JpcHRpb24gPyBlcnJvciA6IGAke2Vycm9yfSAoJHtlcnJvcl9kZXNjcmlwdGlvbn0pYCk7XG5cbiAgICBPYmplY3QuYXNzaWduKFxuICAgICAgdGhpcyxcbiAgICAgIHsgZXJyb3IgfSxcbiAgICAgIGVycm9yX2Rlc2NyaXB0aW9uICYmIHsgZXJyb3JfZGVzY3JpcHRpb24gfSxcbiAgICAgIGVycm9yX3VyaSAmJiB7IGVycm9yX3VyaSB9LFxuICAgICAgc3RhdGUgJiYgeyBzdGF0ZSB9LFxuICAgICAgc2NvcGUgJiYgeyBzY29wZSB9LFxuICAgICAgc2Vzc2lvbl9zdGF0ZSAmJiB7IHNlc3Npb25fc3RhdGUgfSxcbiAgICApO1xuXG4gICAgaWYgKHJlc3BvbnNlKSB7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgJ3Jlc3BvbnNlJywge1xuICAgICAgICB2YWx1ZTogcmVzcG9uc2UsXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICB0aGlzLm5hbWUgPSB0aGlzLmNvbnN0cnVjdG9yLm5hbWU7XG4gICAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UodGhpcywgdGhpcy5jb25zdHJ1Y3Rvcik7XG4gIH1cbn1cblxuY2xhc3MgUlBFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IoLi4uYXJncykge1xuICAgIGlmICh0eXBlb2YgYXJnc1swXSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIHN1cGVyKGZvcm1hdCguLi5hcmdzKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnN0IHsgbWVzc2FnZSwgcHJpbnRmLCByZXNwb25zZSwgLi4ucmVzdCB9ID0gYXJnc1swXTtcbiAgICAgIGlmIChwcmludGYpIHtcbiAgICAgICAgc3VwZXIoZm9ybWF0KC4uLnByaW50ZikpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICB9XG4gICAgICBPYmplY3QuYXNzaWduKHRoaXMsIHJlc3QpO1xuICAgICAgaWYgKHJlc3BvbnNlKSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCAncmVzcG9uc2UnLCB7XG4gICAgICAgICAgdmFsdWU6IHJlc3BvbnNlLFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICB0aGlzLm5hbWUgPSB0aGlzLmNvbnN0cnVjdG9yLm5hbWU7XG4gICAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UodGhpcywgdGhpcy5jb25zdHJ1Y3Rvcik7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIE9QRXJyb3IsXG4gIFJQRXJyb3IsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/assert.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/assert.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("function assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n  if (!issuer[`${endpoint}_endpoint`]) return;\n\n  const eam = `${endpoint}_endpoint_auth_method`;\n  const easa = `${endpoint}_endpoint_auth_signing_alg`;\n  const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n\n  if (properties[eam] && properties[eam].endsWith('_jwt') && !properties[easa] && !issuer[easavs]) {\n    throw new TypeError(\n      `${easavs} must be configured on the issuer if ${easa} is not defined on a client`,\n    );\n  }\n}\n\nfunction assertIssuerConfiguration(issuer, endpoint) {\n  if (!issuer[endpoint]) {\n    throw new TypeError(`${endpoint} must be configured on the issuer`);\n  }\n}\n\nmodule.exports = {\n  assertSigningAlgValuesSupport,\n  assertIssuerConfiguration,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9hc3NlcnQuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxpQkFBaUIsU0FBUzs7QUFFMUIsaUJBQWlCLFNBQVM7QUFDMUIsa0JBQWtCLFNBQVM7QUFDM0Isb0JBQW9CLFNBQVM7O0FBRTdCO0FBQ0E7QUFDQSxTQUFTLFFBQVEsc0NBQXNDLE1BQU07QUFDN0Q7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSwyQkFBMkIsVUFBVTtBQUNyQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hZ2FyYWp1XFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFZvaWNlYmlyZCBuZXdzXFxmbGlwLW5ld3NcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxhc3NlcnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gYXNzZXJ0U2lnbmluZ0FsZ1ZhbHVlc1N1cHBvcnQoZW5kcG9pbnQsIGlzc3VlciwgcHJvcGVydGllcykge1xuICBpZiAoIWlzc3VlcltgJHtlbmRwb2ludH1fZW5kcG9pbnRgXSkgcmV0dXJuO1xuXG4gIGNvbnN0IGVhbSA9IGAke2VuZHBvaW50fV9lbmRwb2ludF9hdXRoX21ldGhvZGA7XG4gIGNvbnN0IGVhc2EgPSBgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9zaWduaW5nX2FsZ2A7XG4gIGNvbnN0IGVhc2F2cyA9IGAke2VuZHBvaW50fV9lbmRwb2ludF9hdXRoX3NpZ25pbmdfYWxnX3ZhbHVlc19zdXBwb3J0ZWRgO1xuXG4gIGlmIChwcm9wZXJ0aWVzW2VhbV0gJiYgcHJvcGVydGllc1tlYW1dLmVuZHNXaXRoKCdfand0JykgJiYgIXByb3BlcnRpZXNbZWFzYV0gJiYgIWlzc3VlcltlYXNhdnNdKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcbiAgICAgIGAke2Vhc2F2c30gbXVzdCBiZSBjb25maWd1cmVkIG9uIHRoZSBpc3N1ZXIgaWYgJHtlYXNhfSBpcyBub3QgZGVmaW5lZCBvbiBhIGNsaWVudGAsXG4gICAgKTtcbiAgfVxufVxuXG5mdW5jdGlvbiBhc3NlcnRJc3N1ZXJDb25maWd1cmF0aW9uKGlzc3VlciwgZW5kcG9pbnQpIHtcbiAgaWYgKCFpc3N1ZXJbZW5kcG9pbnRdKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgJHtlbmRwb2ludH0gbXVzdCBiZSBjb25maWd1cmVkIG9uIHRoZSBpc3N1ZXJgKTtcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgYXNzZXJ0U2lnbmluZ0FsZ1ZhbHVlc1N1cHBvcnQsXG4gIGFzc2VydElzc3VlckNvbmZpZ3VyYXRpb24sXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/base64url.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/base64url.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("let encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = (input, encoding = 'utf8') => Buffer.from(input, encoding).toString('base64url');\n} else {\n  const fromBase64 = (base64) => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = (input, encoding = 'utf8') =>\n    fromBase64(Buffer.from(input, encoding).toString('base64'));\n}\n\nconst decode = (input) => Buffer.from(input, 'base64');\n\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLHFCQUFxQjtBQUNyQixxQkFBcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFnYXJhanVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVm9pY2ViaXJkIG5ld3NcXGZsaXAtbmV3c1xcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGJhc2U2NHVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZW5jb2RlO1xuaWYgKEJ1ZmZlci5pc0VuY29kaW5nKCdiYXNlNjR1cmwnKSkge1xuICBlbmNvZGUgPSAoaW5wdXQsIGVuY29kaW5nID0gJ3V0ZjgnKSA9PiBCdWZmZXIuZnJvbShpbnB1dCwgZW5jb2RpbmcpLnRvU3RyaW5nKCdiYXNlNjR1cmwnKTtcbn0gZWxzZSB7XG4gIGNvbnN0IGZyb21CYXNlNjQgPSAoYmFzZTY0KSA9PiBiYXNlNjQucmVwbGFjZSgvPS9nLCAnJykucmVwbGFjZSgvXFwrL2csICctJykucmVwbGFjZSgvXFwvL2csICdfJyk7XG4gIGVuY29kZSA9IChpbnB1dCwgZW5jb2RpbmcgPSAndXRmOCcpID0+XG4gICAgZnJvbUJhc2U2NChCdWZmZXIuZnJvbShpbnB1dCwgZW5jb2RpbmcpLnRvU3RyaW5nKCdiYXNlNjQnKSk7XG59XG5cbmNvbnN0IGRlY29kZSA9IChpbnB1dCkgPT4gQnVmZmVyLmZyb20oaW5wdXQsICdiYXNlNjQnKTtcblxubW9kdWxlLmV4cG9ydHMuZGVjb2RlID0gZGVjb2RlO1xubW9kdWxlLmV4cG9ydHMuZW5jb2RlID0gZW5jb2RlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/client.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst { random } = __webpack_require__(/*! ./generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst now = __webpack_require__(/*! ./unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst merge = __webpack_require__(/*! ./merge */ \"(rsc)/./node_modules/openid-client/lib/helpers/merge.js\");\n\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = (value) => encodeURIComponent(value).replace(/%20/g, '+');\n\nasync function clientAssertion(endpoint, payload) {\n  let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n  if (!alg) {\n    assertIssuerConfiguration(\n      this.issuer,\n      `${endpoint}_endpoint_auth_signing_alg_values_supported`,\n    );\n  }\n\n  if (this[`${endpoint}_endpoint_auth_method`] === 'client_secret_jwt') {\n    if (!alg) {\n      const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n      alg =\n        Array.isArray(supported) && supported.find((signAlg) => /^HS(?:256|384|512)/.test(signAlg));\n    }\n\n    if (!alg) {\n      throw new RPError(\n        `failed to determine a JWS Algorithm to use for ${\n          this[`${endpoint}_endpoint_auth_method`]\n        } Client Assertion`,\n      );\n    }\n\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n      .setProtectedHeader({ alg })\n      .sign(this.secretForAlg(alg));\n  }\n\n  const keystore = await keystores.get(this);\n\n  if (!keystore) {\n    throw new TypeError('no client jwks provided for signing a client assertion with');\n  }\n\n  if (!alg) {\n    const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n    alg =\n      Array.isArray(supported) &&\n      supported.find((signAlg) => keystore.get({ alg: signAlg, use: 'sig' }));\n  }\n\n  if (!alg) {\n    throw new RPError(\n      `failed to determine a JWS Algorithm to use for ${\n        this[`${endpoint}_endpoint_auth_method`]\n      } Client Assertion`,\n    );\n  }\n\n  const key = keystore.get({ alg, use: 'sig' });\n  if (!key) {\n    throw new RPError(\n      `no key found in client jwks to sign a client assertion with using alg ${alg}`,\n    );\n  }\n\n  return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n    .setProtectedHeader({ alg, kid: key.jwk && key.jwk.kid })\n    .sign(await key.keyObject(alg));\n}\n\nasync function authFor(endpoint, { clientAssertionPayload } = {}) {\n  const authMethod = this[`${endpoint}_endpoint_auth_method`];\n  switch (authMethod) {\n    case 'self_signed_tls_client_auth':\n    case 'tls_client_auth':\n    case 'none':\n      return { form: { client_id: this.client_id } };\n    case 'client_secret_post':\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_post client authentication method requires a client_secret',\n        );\n      }\n      return { form: { client_id: this.client_id, client_secret: this.client_secret } };\n    case 'private_key_jwt':\n    case 'client_secret_jwt': {\n      const timestamp = now();\n\n      const assertion = await clientAssertion.call(this, endpoint, {\n        iat: timestamp,\n        exp: timestamp + 60,\n        jti: random(),\n        iss: this.client_id,\n        sub: this.client_id,\n        aud: this.issuer.issuer,\n        ...clientAssertionPayload,\n      });\n\n      return {\n        form: {\n          client_id: this.client_id,\n          client_assertion: assertion,\n          client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',\n        },\n      };\n    }\n    case 'client_secret_basic': {\n      // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n      // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n      // > The client identifier is encoded using the\n      // > \"application/x-www-form-urlencoded\" encoding algorithm per\n      // > Appendix B, and the encoded value is used as the username; the client\n      // > password is encoded using the same algorithm and used as the\n      // > password.\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_basic client authentication method requires a client_secret',\n        );\n      }\n      const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n      const value = Buffer.from(encoded).toString('base64');\n      return { headers: { Authorization: `Basic ${value}` } };\n    }\n    default: {\n      throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n    }\n  }\n}\n\nfunction resolveResponseType() {\n  const { length, 0: value } = this.response_types;\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nfunction resolveRedirectUri() {\n  const { length, 0: value } = this.redirect_uris || [];\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nasync function authenticatedPost(\n  endpoint,\n  opts,\n  { clientAssertionPayload, endpointAuthMethod = endpoint, DPoP } = {},\n) {\n  const auth = await authFor.call(this, endpointAuthMethod, { clientAssertionPayload });\n  const requestOpts = merge(opts, auth);\n\n  const mTLS =\n    this[`${endpointAuthMethod}_endpoint_auth_method`].includes('tls_client_auth') ||\n    (endpoint === 'token' && this.tls_client_certificate_bound_access_tokens);\n\n  let targetUrl;\n  if (mTLS && this.issuer.mtls_endpoint_aliases) {\n    targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n  }\n\n  targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n\n  if ('form' in requestOpts) {\n    for (const [key, value] of Object.entries(requestOpts.form)) {\n      if (typeof value === 'undefined') {\n        delete requestOpts.form[key];\n      }\n    }\n  }\n\n  return request.call(\n    this,\n    {\n      ...requestOpts,\n      method: 'POST',\n      url: targetUrl,\n      headers: {\n        ...(endpoint !== 'revocation'\n          ? {\n              Accept: 'application/json',\n            }\n          : undefined),\n        ...requestOpts.headers,\n      },\n    },\n    { mTLS, DPoP },\n  );\n}\n\nmodule.exports = {\n  resolveResponseType,\n  resolveRedirectUri,\n  authFor,\n  authenticatedPost,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jbGllbnQuanMiLCJtYXBwaW5ncyI6IkFBQUEsYUFBYSxtQkFBTyxDQUFDLDhEQUFNOztBQUUzQixRQUFRLFVBQVUsRUFBRSxtQkFBTyxDQUFDLG1FQUFXOztBQUV2QyxRQUFRLDRCQUE0QixFQUFFLG1CQUFPLENBQUMsMEVBQVU7QUFDeEQsUUFBUSxTQUFTLEVBQUUsbUJBQU8sQ0FBQyxrRkFBYztBQUN6QyxZQUFZLG1CQUFPLENBQUMsMEZBQWtCO0FBQ3RDLGdCQUFnQixtQkFBTyxDQUFDLDRFQUFXO0FBQ25DLFFBQVEsWUFBWSxFQUFFLG1CQUFPLENBQUMsa0ZBQWM7QUFDNUMsY0FBYyxtQkFBTyxDQUFDLHdFQUFTOztBQUUvQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IsU0FBUztBQUM3QjtBQUNBO0FBQ0E7QUFDQSxTQUFTLFNBQVM7QUFDbEI7QUFDQTs7QUFFQSxjQUFjLFNBQVM7QUFDdkI7QUFDQSx1Q0FBdUMsU0FBUztBQUNoRDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFNBQVM7QUFDM0IsVUFBVTtBQUNWO0FBQ0E7O0FBRUE7QUFDQSw0QkFBNEIsS0FBSztBQUNqQztBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHFDQUFxQyxTQUFTO0FBQzlDO0FBQ0E7QUFDQSxpREFBaUQsMEJBQTBCO0FBQzNFOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixTQUFTO0FBQ3pCLFFBQVE7QUFDUjtBQUNBOztBQUVBLDZCQUE2QixpQkFBaUI7QUFDOUM7QUFDQTtBQUNBLCtFQUErRSxJQUFJO0FBQ25GO0FBQ0E7O0FBRUE7QUFDQSwwQkFBMEIsa0NBQWtDO0FBQzVEO0FBQ0E7O0FBRUEsbUNBQW1DLHlCQUF5QixJQUFJO0FBQ2hFLDZCQUE2QixTQUFTO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87O0FBRVA7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNFQUFzRTtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5Qiw4QkFBOEIsR0FBRyxrQ0FBa0M7QUFDNUY7QUFDQSxlQUFlLFdBQVcsd0JBQXdCLE1BQU07QUFDeEQ7QUFDQTtBQUNBLHNEQUFzRCxTQUFTO0FBQy9EO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFVBQVUsbUJBQW1COztBQUU3QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLFVBQVUsbUJBQW1COztBQUU3QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDhEQUE4RCxJQUFJO0FBQ3RFO0FBQ0EsOERBQThELHdCQUF3QjtBQUN0Rjs7QUFFQTtBQUNBLFlBQVksbUJBQW1CO0FBQy9COztBQUVBO0FBQ0E7QUFDQSxxREFBcUQsU0FBUztBQUM5RDs7QUFFQSwwQ0FBMEMsU0FBUzs7QUFFbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMLE1BQU0sWUFBWTtBQUNsQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWdhcmFqdVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxWb2ljZWJpcmQgbmV3c1xcZmxpcC1uZXdzXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcY2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGpvc2UgPSByZXF1aXJlKCdqb3NlJyk7XG5cbmNvbnN0IHsgUlBFcnJvciB9ID0gcmVxdWlyZSgnLi4vZXJyb3JzJyk7XG5cbmNvbnN0IHsgYXNzZXJ0SXNzdWVyQ29uZmlndXJhdGlvbiB9ID0gcmVxdWlyZSgnLi9hc3NlcnQnKTtcbmNvbnN0IHsgcmFuZG9tIH0gPSByZXF1aXJlKCcuL2dlbmVyYXRvcnMnKTtcbmNvbnN0IG5vdyA9IHJlcXVpcmUoJy4vdW5peF90aW1lc3RhbXAnKTtcbmNvbnN0IHJlcXVlc3QgPSByZXF1aXJlKCcuL3JlcXVlc3QnKTtcbmNvbnN0IHsga2V5c3RvcmVzIH0gPSByZXF1aXJlKCcuL3dlYWtfY2FjaGUnKTtcbmNvbnN0IG1lcmdlID0gcmVxdWlyZSgnLi9tZXJnZScpO1xuXG4vLyBUT0RPOiBpbiB2Ni54IGFkZGl0aW9uYWxseSBlbmNvZGUgdGhlIGAtIF8gLiAhIH4gKiAnICggKWAgY2hhcmFjdGVyc1xuLy8gaHR0cHM6Ly9naXRodWIuY29tL3BhbnZhL25vZGUtb3BlbmlkLWNsaWVudC9jb21taXQvNWEyZWE4MGVmNWU1OWVjMGMwM2RiZDk3ZDgyZjU1MWUyNGE5ZDM0OFxuY29uc3QgZm9ybVVybEVuY29kZSA9ICh2YWx1ZSkgPT4gZW5jb2RlVVJJQ29tcG9uZW50KHZhbHVlKS5yZXBsYWNlKC8lMjAvZywgJysnKTtcblxuYXN5bmMgZnVuY3Rpb24gY2xpZW50QXNzZXJ0aW9uKGVuZHBvaW50LCBwYXlsb2FkKSB7XG4gIGxldCBhbGcgPSB0aGlzW2Ake2VuZHBvaW50fV9lbmRwb2ludF9hdXRoX3NpZ25pbmdfYWxnYF07XG4gIGlmICghYWxnKSB7XG4gICAgYXNzZXJ0SXNzdWVyQ29uZmlndXJhdGlvbihcbiAgICAgIHRoaXMuaXNzdWVyLFxuICAgICAgYCR7ZW5kcG9pbnR9X2VuZHBvaW50X2F1dGhfc2lnbmluZ19hbGdfdmFsdWVzX3N1cHBvcnRlZGAsXG4gICAgKTtcbiAgfVxuXG4gIGlmICh0aGlzW2Ake2VuZHBvaW50fV9lbmRwb2ludF9hdXRoX21ldGhvZGBdID09PSAnY2xpZW50X3NlY3JldF9qd3QnKSB7XG4gICAgaWYgKCFhbGcpIHtcbiAgICAgIGNvbnN0IHN1cHBvcnRlZCA9IHRoaXMuaXNzdWVyW2Ake2VuZHBvaW50fV9lbmRwb2ludF9hdXRoX3NpZ25pbmdfYWxnX3ZhbHVlc19zdXBwb3J0ZWRgXTtcbiAgICAgIGFsZyA9XG4gICAgICAgIEFycmF5LmlzQXJyYXkoc3VwcG9ydGVkKSAmJiBzdXBwb3J0ZWQuZmluZCgoc2lnbkFsZykgPT4gL15IUyg/OjI1NnwzODR8NTEyKS8udGVzdChzaWduQWxnKSk7XG4gICAgfVxuXG4gICAgaWYgKCFhbGcpIHtcbiAgICAgIHRocm93IG5ldyBSUEVycm9yKFxuICAgICAgICBgZmFpbGVkIHRvIGRldGVybWluZSBhIEpXUyBBbGdvcml0aG0gdG8gdXNlIGZvciAke1xuICAgICAgICAgIHRoaXNbYCR7ZW5kcG9pbnR9X2VuZHBvaW50X2F1dGhfbWV0aG9kYF1cbiAgICAgICAgfSBDbGllbnQgQXNzZXJ0aW9uYCxcbiAgICAgICk7XG4gICAgfVxuXG4gICAgcmV0dXJuIG5ldyBqb3NlLkNvbXBhY3RTaWduKEJ1ZmZlci5mcm9tKEpTT04uc3RyaW5naWZ5KHBheWxvYWQpKSlcbiAgICAgIC5zZXRQcm90ZWN0ZWRIZWFkZXIoeyBhbGcgfSlcbiAgICAgIC5zaWduKHRoaXMuc2VjcmV0Rm9yQWxnKGFsZykpO1xuICB9XG5cbiAgY29uc3Qga2V5c3RvcmUgPSBhd2FpdCBrZXlzdG9yZXMuZ2V0KHRoaXMpO1xuXG4gIGlmICgha2V5c3RvcmUpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdubyBjbGllbnQgandrcyBwcm92aWRlZCBmb3Igc2lnbmluZyBhIGNsaWVudCBhc3NlcnRpb24gd2l0aCcpO1xuICB9XG5cbiAgaWYgKCFhbGcpIHtcbiAgICBjb25zdCBzdXBwb3J0ZWQgPSB0aGlzLmlzc3VlcltgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9zaWduaW5nX2FsZ192YWx1ZXNfc3VwcG9ydGVkYF07XG4gICAgYWxnID1cbiAgICAgIEFycmF5LmlzQXJyYXkoc3VwcG9ydGVkKSAmJlxuICAgICAgc3VwcG9ydGVkLmZpbmQoKHNpZ25BbGcpID0+IGtleXN0b3JlLmdldCh7IGFsZzogc2lnbkFsZywgdXNlOiAnc2lnJyB9KSk7XG4gIH1cblxuICBpZiAoIWFsZykge1xuICAgIHRocm93IG5ldyBSUEVycm9yKFxuICAgICAgYGZhaWxlZCB0byBkZXRlcm1pbmUgYSBKV1MgQWxnb3JpdGhtIHRvIHVzZSBmb3IgJHtcbiAgICAgICAgdGhpc1tgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9tZXRob2RgXVxuICAgICAgfSBDbGllbnQgQXNzZXJ0aW9uYCxcbiAgICApO1xuICB9XG5cbiAgY29uc3Qga2V5ID0ga2V5c3RvcmUuZ2V0KHsgYWxnLCB1c2U6ICdzaWcnIH0pO1xuICBpZiAoIWtleSkge1xuICAgIHRocm93IG5ldyBSUEVycm9yKFxuICAgICAgYG5vIGtleSBmb3VuZCBpbiBjbGllbnQgandrcyB0byBzaWduIGEgY2xpZW50IGFzc2VydGlvbiB3aXRoIHVzaW5nIGFsZyAke2FsZ31gLFxuICAgICk7XG4gIH1cblxuICByZXR1cm4gbmV3IGpvc2UuQ29tcGFjdFNpZ24oQnVmZmVyLmZyb20oSlNPTi5zdHJpbmdpZnkocGF5bG9hZCkpKVxuICAgIC5zZXRQcm90ZWN0ZWRIZWFkZXIoeyBhbGcsIGtpZDoga2V5Lmp3ayAmJiBrZXkuandrLmtpZCB9KVxuICAgIC5zaWduKGF3YWl0IGtleS5rZXlPYmplY3QoYWxnKSk7XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGF1dGhGb3IoZW5kcG9pbnQsIHsgY2xpZW50QXNzZXJ0aW9uUGF5bG9hZCB9ID0ge30pIHtcbiAgY29uc3QgYXV0aE1ldGhvZCA9IHRoaXNbYCR7ZW5kcG9pbnR9X2VuZHBvaW50X2F1dGhfbWV0aG9kYF07XG4gIHN3aXRjaCAoYXV0aE1ldGhvZCkge1xuICAgIGNhc2UgJ3NlbGZfc2lnbmVkX3Rsc19jbGllbnRfYXV0aCc6XG4gICAgY2FzZSAndGxzX2NsaWVudF9hdXRoJzpcbiAgICBjYXNlICdub25lJzpcbiAgICAgIHJldHVybiB7IGZvcm06IHsgY2xpZW50X2lkOiB0aGlzLmNsaWVudF9pZCB9IH07XG4gICAgY2FzZSAnY2xpZW50X3NlY3JldF9wb3N0JzpcbiAgICAgIGlmICh0eXBlb2YgdGhpcy5jbGllbnRfc2VjcmV0ICE9PSAnc3RyaW5nJykge1xuICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFxuICAgICAgICAgICdjbGllbnRfc2VjcmV0X3Bvc3QgY2xpZW50IGF1dGhlbnRpY2F0aW9uIG1ldGhvZCByZXF1aXJlcyBhIGNsaWVudF9zZWNyZXQnLFxuICAgICAgICApO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHsgZm9ybTogeyBjbGllbnRfaWQ6IHRoaXMuY2xpZW50X2lkLCBjbGllbnRfc2VjcmV0OiB0aGlzLmNsaWVudF9zZWNyZXQgfSB9O1xuICAgIGNhc2UgJ3ByaXZhdGVfa2V5X2p3dCc6XG4gICAgY2FzZSAnY2xpZW50X3NlY3JldF9qd3QnOiB7XG4gICAgICBjb25zdCB0aW1lc3RhbXAgPSBub3coKTtcblxuICAgICAgY29uc3QgYXNzZXJ0aW9uID0gYXdhaXQgY2xpZW50QXNzZXJ0aW9uLmNhbGwodGhpcywgZW5kcG9pbnQsIHtcbiAgICAgICAgaWF0OiB0aW1lc3RhbXAsXG4gICAgICAgIGV4cDogdGltZXN0YW1wICsgNjAsXG4gICAgICAgIGp0aTogcmFuZG9tKCksXG4gICAgICAgIGlzczogdGhpcy5jbGllbnRfaWQsXG4gICAgICAgIHN1YjogdGhpcy5jbGllbnRfaWQsXG4gICAgICAgIGF1ZDogdGhpcy5pc3N1ZXIuaXNzdWVyLFxuICAgICAgICAuLi5jbGllbnRBc3NlcnRpb25QYXlsb2FkLFxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGZvcm06IHtcbiAgICAgICAgICBjbGllbnRfaWQ6IHRoaXMuY2xpZW50X2lkLFxuICAgICAgICAgIGNsaWVudF9hc3NlcnRpb246IGFzc2VydGlvbixcbiAgICAgICAgICBjbGllbnRfYXNzZXJ0aW9uX3R5cGU6ICd1cm46aWV0ZjpwYXJhbXM6b2F1dGg6Y2xpZW50LWFzc2VydGlvbi10eXBlOmp3dC1iZWFyZXInLFxuICAgICAgICB9LFxuICAgICAgfTtcbiAgICB9XG4gICAgY2FzZSAnY2xpZW50X3NlY3JldF9iYXNpYyc6IHtcbiAgICAgIC8vIFRoaXMgaXMgY29ycmVjdCBiZWhhdmlvdXIsIHNlZSBodHRwczovL3Rvb2xzLmlldGYub3JnL2h0bWwvcmZjNjc0OSNzZWN0aW9uLTIuMy4xIGFuZCB0aGVcbiAgICAgIC8vIHJlbGF0ZWQgYXBwZW5kaXguIChhbHNvIGh0dHBzOi8vZ2l0aHViLmNvbS9wYW52YS9ub2RlLW9wZW5pZC1jbGllbnQvcHVsbC85MSlcbiAgICAgIC8vID4gVGhlIGNsaWVudCBpZGVudGlmaWVyIGlzIGVuY29kZWQgdXNpbmcgdGhlXG4gICAgICAvLyA+IFwiYXBwbGljYXRpb24veC13d3ctZm9ybS11cmxlbmNvZGVkXCIgZW5jb2RpbmcgYWxnb3JpdGhtIHBlclxuICAgICAgLy8gPiBBcHBlbmRpeCBCLCBhbmQgdGhlIGVuY29kZWQgdmFsdWUgaXMgdXNlZCBhcyB0aGUgdXNlcm5hbWU7IHRoZSBjbGllbnRcbiAgICAgIC8vID4gcGFzc3dvcmQgaXMgZW5jb2RlZCB1c2luZyB0aGUgc2FtZSBhbGdvcml0aG0gYW5kIHVzZWQgYXMgdGhlXG4gICAgICAvLyA+IHBhc3N3b3JkLlxuICAgICAgaWYgKHR5cGVvZiB0aGlzLmNsaWVudF9zZWNyZXQgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXG4gICAgICAgICAgJ2NsaWVudF9zZWNyZXRfYmFzaWMgY2xpZW50IGF1dGhlbnRpY2F0aW9uIG1ldGhvZCByZXF1aXJlcyBhIGNsaWVudF9zZWNyZXQnLFxuICAgICAgICApO1xuICAgICAgfVxuICAgICAgY29uc3QgZW5jb2RlZCA9IGAke2Zvcm1VcmxFbmNvZGUodGhpcy5jbGllbnRfaWQpfToke2Zvcm1VcmxFbmNvZGUodGhpcy5jbGllbnRfc2VjcmV0KX1gO1xuICAgICAgY29uc3QgdmFsdWUgPSBCdWZmZXIuZnJvbShlbmNvZGVkKS50b1N0cmluZygnYmFzZTY0Jyk7XG4gICAgICByZXR1cm4geyBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCYXNpYyAke3ZhbHVlfWAgfSB9O1xuICAgIH1cbiAgICBkZWZhdWx0OiB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGBtaXNzaW5nLCBvciB1bnN1cHBvcnRlZCwgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9tZXRob2RgKTtcbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gcmVzb2x2ZVJlc3BvbnNlVHlwZSgpIHtcbiAgY29uc3QgeyBsZW5ndGgsIDA6IHZhbHVlIH0gPSB0aGlzLnJlc3BvbnNlX3R5cGVzO1xuXG4gIGlmIChsZW5ndGggPT09IDEpIHtcbiAgICByZXR1cm4gdmFsdWU7XG4gIH1cblxuICByZXR1cm4gdW5kZWZpbmVkO1xufVxuXG5mdW5jdGlvbiByZXNvbHZlUmVkaXJlY3RVcmkoKSB7XG4gIGNvbnN0IHsgbGVuZ3RoLCAwOiB2YWx1ZSB9ID0gdGhpcy5yZWRpcmVjdF91cmlzIHx8IFtdO1xuXG4gIGlmIChsZW5ndGggPT09IDEpIHtcbiAgICByZXR1cm4gdmFsdWU7XG4gIH1cblxuICByZXR1cm4gdW5kZWZpbmVkO1xufVxuXG5hc3luYyBmdW5jdGlvbiBhdXRoZW50aWNhdGVkUG9zdChcbiAgZW5kcG9pbnQsXG4gIG9wdHMsXG4gIHsgY2xpZW50QXNzZXJ0aW9uUGF5bG9hZCwgZW5kcG9pbnRBdXRoTWV0aG9kID0gZW5kcG9pbnQsIERQb1AgfSA9IHt9LFxuKSB7XG4gIGNvbnN0IGF1dGggPSBhd2FpdCBhdXRoRm9yLmNhbGwodGhpcywgZW5kcG9pbnRBdXRoTWV0aG9kLCB7IGNsaWVudEFzc2VydGlvblBheWxvYWQgfSk7XG4gIGNvbnN0IHJlcXVlc3RPcHRzID0gbWVyZ2Uob3B0cywgYXV0aCk7XG5cbiAgY29uc3QgbVRMUyA9XG4gICAgdGhpc1tgJHtlbmRwb2ludEF1dGhNZXRob2R9X2VuZHBvaW50X2F1dGhfbWV0aG9kYF0uaW5jbHVkZXMoJ3Rsc19jbGllbnRfYXV0aCcpIHx8XG4gICAgKGVuZHBvaW50ID09PSAndG9rZW4nICYmIHRoaXMudGxzX2NsaWVudF9jZXJ0aWZpY2F0ZV9ib3VuZF9hY2Nlc3NfdG9rZW5zKTtcblxuICBsZXQgdGFyZ2V0VXJsO1xuICBpZiAobVRMUyAmJiB0aGlzLmlzc3Vlci5tdGxzX2VuZHBvaW50X2FsaWFzZXMpIHtcbiAgICB0YXJnZXRVcmwgPSB0aGlzLmlzc3Vlci5tdGxzX2VuZHBvaW50X2FsaWFzZXNbYCR7ZW5kcG9pbnR9X2VuZHBvaW50YF07XG4gIH1cblxuICB0YXJnZXRVcmwgPSB0YXJnZXRVcmwgfHwgdGhpcy5pc3N1ZXJbYCR7ZW5kcG9pbnR9X2VuZHBvaW50YF07XG5cbiAgaWYgKCdmb3JtJyBpbiByZXF1ZXN0T3B0cykge1xuICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHJlcXVlc3RPcHRzLmZvcm0pKSB7XG4gICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICBkZWxldGUgcmVxdWVzdE9wdHMuZm9ybVtrZXldO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiByZXF1ZXN0LmNhbGwoXG4gICAgdGhpcyxcbiAgICB7XG4gICAgICAuLi5yZXF1ZXN0T3B0cyxcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgdXJsOiB0YXJnZXRVcmwsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIC4uLihlbmRwb2ludCAhPT0gJ3Jldm9jYXRpb24nXG4gICAgICAgICAgPyB7XG4gICAgICAgICAgICAgIEFjY2VwdDogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgfVxuICAgICAgICAgIDogdW5kZWZpbmVkKSxcbiAgICAgICAgLi4ucmVxdWVzdE9wdHMuaGVhZGVycyxcbiAgICAgIH0sXG4gICAgfSxcbiAgICB7IG1UTFMsIERQb1AgfSxcbiAgKTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIHJlc29sdmVSZXNwb25zZVR5cGUsXG4gIHJlc29sdmVSZWRpcmVjdFVyaSxcbiAgYXV0aEZvcixcbiAgYXV0aGVudGljYXRlZFBvc3QsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/consts.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/consts.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("const HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\n\nmodule.exports = {\n  CLOCK_TOLERANCE,\n  HTTP_OPTIONS,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jb25zdHMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWdhcmFqdVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxWb2ljZWJpcmQgbmV3c1xcZmxpcC1uZXdzXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcY29uc3RzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEhUVFBfT1BUSU9OUyA9IFN5bWJvbCgpO1xuY29uc3QgQ0xPQ0tfVE9MRVJBTkNFID0gU3ltYm9sKCk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBDTE9DS19UT0xFUkFOQ0UsXG4gIEhUVFBfT1BUSU9OUyxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/consts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/decode_jwt.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nmodule.exports = (token) => {\n  if (typeof token !== 'string' || !token) {\n    throw new TypeError('JWT must be a string');\n  }\n\n  const { 0: header, 1: payload, 2: signature, length } = token.split('.');\n\n  if (length === 5) {\n    throw new TypeError('encrypted JWTs cannot be decoded');\n  }\n\n  if (length !== 3) {\n    throw new Error('JWTs must have three components');\n  }\n\n  try {\n    return {\n      header: JSON.parse(base64url.decode(header)),\n      payload: JSON.parse(base64url.decode(payload)),\n      signature,\n    };\n  } catch (err) {\n    throw new Error('JWT is malformed');\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWNvZGVfand0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLGdGQUFhOztBQUV2QztBQUNBO0FBQ0E7QUFDQTs7QUFFQSxVQUFVLDhDQUE4Qzs7QUFFeEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hZ2FyYWp1XFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFZvaWNlYmlyZCBuZXdzXFxmbGlwLW5ld3NcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxkZWNvZGVfand0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vYmFzZTY0dXJsJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gKHRva2VuKSA9PiB7XG4gIGlmICh0eXBlb2YgdG9rZW4gIT09ICdzdHJpbmcnIHx8ICF0b2tlbikge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0pXVCBtdXN0IGJlIGEgc3RyaW5nJyk7XG4gIH1cblxuICBjb25zdCB7IDA6IGhlYWRlciwgMTogcGF5bG9hZCwgMjogc2lnbmF0dXJlLCBsZW5ndGggfSA9IHRva2VuLnNwbGl0KCcuJyk7XG5cbiAgaWYgKGxlbmd0aCA9PT0gNSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2VuY3J5cHRlZCBKV1RzIGNhbm5vdCBiZSBkZWNvZGVkJyk7XG4gIH1cblxuICBpZiAobGVuZ3RoICE9PSAzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdKV1RzIG11c3QgaGF2ZSB0aHJlZSBjb21wb25lbnRzJyk7XG4gIH1cblxuICB0cnkge1xuICAgIHJldHVybiB7XG4gICAgICBoZWFkZXI6IEpTT04ucGFyc2UoYmFzZTY0dXJsLmRlY29kZShoZWFkZXIpKSxcbiAgICAgIHBheWxvYWQ6IEpTT04ucGFyc2UoYmFzZTY0dXJsLmRlY29kZShwYXlsb2FkKSksXG4gICAgICBzaWduYXR1cmUsXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdKV1QgaXMgbWFsZm9ybWVkJyk7XG4gIH1cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/deep_clone.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports = globalThis.structuredClone || ((obj) => JSON.parse(JSON.stringify(obj)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWVwX2Nsb25lLmpzIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hZ2FyYWp1XFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFZvaWNlYmlyZCBuZXdzXFxmbGlwLW5ld3NcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxkZWVwX2Nsb25lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZ2xvYmFsVGhpcy5zdHJ1Y3R1cmVkQ2xvbmUgfHwgKChvYmopID0+IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkob2JqKSkpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/defaults.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/defaults.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction defaults(deep, target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (typeof target[key] === 'undefined' && typeof value !== 'undefined') {\n        target[key] = value;\n      }\n\n      if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n        defaults(true, target[key], value);\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWZhdWx0cy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLG1CQUFtQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWdhcmFqdVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxWb2ljZWJpcmQgbmV3c1xcZmxpcC1uZXdzXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcZGVmYXVsdHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNQbGFpbk9iamVjdCA9IHJlcXVpcmUoJy4vaXNfcGxhaW5fb2JqZWN0Jyk7XG5cbmZ1bmN0aW9uIGRlZmF1bHRzKGRlZXAsIHRhcmdldCwgLi4uc291cmNlcykge1xuICBmb3IgKGNvbnN0IHNvdXJjZSBvZiBzb3VyY2VzKSB7XG4gICAgaWYgKCFpc1BsYWluT2JqZWN0KHNvdXJjZSkpIHtcbiAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhzb3VyY2UpKSB7XG4gICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgaWYgKi9cbiAgICAgIGlmIChrZXkgPT09ICdfX3Byb3RvX18nIHx8IGtleSA9PT0gJ2NvbnN0cnVjdG9yJykge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGlmICh0eXBlb2YgdGFyZ2V0W2tleV0gPT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiB2YWx1ZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSB2YWx1ZTtcbiAgICAgIH1cblxuICAgICAgaWYgKGRlZXAgJiYgaXNQbGFpbk9iamVjdCh0YXJnZXRba2V5XSkgJiYgaXNQbGFpbk9iamVjdCh2YWx1ZSkpIHtcbiAgICAgICAgZGVmYXVsdHModHJ1ZSwgdGFyZ2V0W2tleV0sIHZhbHVlKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gdGFyZ2V0O1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGRlZmF1bHRzLmJpbmQodW5kZWZpbmVkLCBmYWxzZSk7XG5tb2R1bGUuZXhwb3J0cy5kZWVwID0gZGVmYXVsdHMuYmluZCh1bmRlZmluZWQsIHRydWUpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/generators.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/generators.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { createHash, randomBytes } = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nconst random = (bytes = 32) => base64url.encode(randomBytes(bytes));\n\nmodule.exports = {\n  random,\n  state: random,\n  nonce: random,\n  codeVerifier: random,\n  codeChallenge: (codeVerifier) =>\n    base64url.encode(createHash('sha256').update(codeVerifier).digest()),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsMEJBQTBCLEVBQUUsbUJBQU8sQ0FBQyxzQkFBUTs7QUFFcEQsa0JBQWtCLG1CQUFPLENBQUMsZ0ZBQWE7O0FBRXZDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFnYXJhanVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVm9pY2ViaXJkIG5ld3NcXGZsaXAtbmV3c1xcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGdlbmVyYXRvcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBjcmVhdGVIYXNoLCByYW5kb21CeXRlcyB9ID0gcmVxdWlyZSgnY3J5cHRvJyk7XG5cbmNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vYmFzZTY0dXJsJyk7XG5cbmNvbnN0IHJhbmRvbSA9IChieXRlcyA9IDMyKSA9PiBiYXNlNjR1cmwuZW5jb2RlKHJhbmRvbUJ5dGVzKGJ5dGVzKSk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICByYW5kb20sXG4gIHN0YXRlOiByYW5kb20sXG4gIG5vbmNlOiByYW5kb20sXG4gIGNvZGVWZXJpZmllcjogcmFuZG9tLFxuICBjb2RlQ2hhbGxlbmdlOiAoY29kZVZlcmlmaWVyKSA9PlxuICAgIGJhc2U2NHVybC5lbmNvZGUoY3JlYXRlSGFzaCgnc2hhMjU2JykudXBkYXRlKGNvZGVWZXJpZmllcikuZGlnZXN0KCkpLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/generators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js":
/*!*****************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_key_object.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const util = __webpack_require__(/*! util */ \"util\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\n\nmodule.exports = util.types.isKeyObject || ((obj) => obj && obj instanceof crypto.KeyObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGFBQWEsbUJBQU8sQ0FBQyxrQkFBTTtBQUMzQixlQUFlLG1CQUFPLENBQUMsc0JBQVE7O0FBRS9CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hZ2FyYWp1XFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFZvaWNlYmlyZCBuZXdzXFxmbGlwLW5ld3NcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxpc19rZXlfb2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHV0aWwgPSByZXF1aXJlKCd1dGlsJyk7XG5jb25zdCBjcnlwdG8gPSByZXF1aXJlKCdjcnlwdG8nKTtcblxubW9kdWxlLmV4cG9ydHMgPSB1dGlsLnR5cGVzLmlzS2V5T2JqZWN0IHx8ICgob2JqKSA9PiBvYmogJiYgb2JqIGluc3RhbmNlb2YgY3J5cHRvLktleU9iamVjdCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js":
/*!*******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_plain_object.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("module.exports = (a) => !!a && a.constructor === Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanMiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFnYXJhanVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVm9pY2ViaXJkIG5ld3NcXGZsaXAtbmV3c1xcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGlzX3BsYWluX29iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IChhKSA9PiAhIWEgJiYgYS5jb25zdHJ1Y3RvciA9PT0gT2JqZWN0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/issuer.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/issuer.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const objectHash = __webpack_require__(/*! object-hash */ \"(rsc)/./node_modules/object-hash/index.js\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst KeyStore = __webpack_require__(/*! ./keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst processResponse = __webpack_require__(/*! ./process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = (ctx) => {\n  if (!caches.has(ctx)) {\n    caches.set(ctx, new LRU({ max: 100 }));\n  }\n  return caches.get(ctx);\n};\n\nasync function getKeyStore(reload = false) {\n  assertIssuerConfiguration(this, 'jwks_uri');\n\n  const keystore = keystores.get(this);\n  const cache = lrus(this);\n\n  if (reload || !keystore) {\n    if (inFlight.has(this)) {\n      return inFlight.get(this);\n    }\n    cache.reset();\n    inFlight.set(\n      this,\n      (async () => {\n        const response = await request\n          .call(this, {\n            method: 'GET',\n            responseType: 'json',\n            url: this.jwks_uri,\n            headers: {\n              Accept: 'application/json, application/jwk-set+json',\n            },\n          })\n          .finally(() => {\n            inFlight.delete(this);\n          });\n        const jwks = processResponse(response);\n\n        const joseKeyStore = KeyStore.fromJWKS(jwks, { onlyPublic: true });\n        cache.set('throttle', true, 60 * 1000);\n        keystores.set(this, joseKeyStore);\n\n        return joseKeyStore;\n      })(),\n    );\n\n    return inFlight.get(this);\n  }\n\n  return keystore;\n}\n\nasync function queryKeyStore({ kid, kty, alg, use }, { allowMulti = false } = {}) {\n  const cache = lrus(this);\n\n  const def = {\n    kid,\n    kty,\n    alg,\n    use,\n  };\n\n  const defHash = objectHash(def, {\n    algorithm: 'sha256',\n    ignoreUnknown: true,\n    unorderedArrays: true,\n    unorderedSets: true,\n    respectType: false,\n  });\n\n  // refresh keystore on every unknown key but also only upto once every minute\n  const freshJwksUri = cache.get(defHash) || cache.get('throttle');\n\n  const keystore = await getKeyStore.call(this, !freshJwksUri);\n  const keys = keystore.all(def);\n\n  delete def.use;\n  if (keys.length === 0) {\n    throw new RPError({\n      printf: [\"no valid key found in issuer's jwks_uri for key parameters %j\", def],\n      jwks: keystore,\n    });\n  }\n\n  if (!allowMulti && keys.length > 1 && !kid) {\n    throw new RPError({\n      printf: [\n        \"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\",\n        def,\n      ],\n      jwks: keystore,\n    });\n  }\n\n  cache.set(defHash, true);\n\n  return keys;\n}\n\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/keystore.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/keystore.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst clone = __webpack_require__(/*! ./deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nconst internal = Symbol();\n\nconst keyscore = (key, { alg, use }) => {\n  let score = 0;\n\n  if (alg && key.alg) {\n    score++;\n  }\n\n  if (use && key.use) {\n    score++;\n  }\n\n  return score;\n};\n\nfunction getKtyFromAlg(alg) {\n  switch (typeof alg === 'string' && alg.slice(0, 2)) {\n    case 'RS':\n    case 'PS':\n      return 'RSA';\n    case 'ES':\n      return 'EC';\n    case 'Ed':\n      return 'OKP';\n    default:\n      return undefined;\n  }\n}\n\nfunction getAlgorithms(use, alg, kty, crv) {\n  // Ed25519, Ed448, and secp256k1 always have \"alg\"\n  // OKP always has \"use\"\n  if (alg) {\n    return new Set([alg]);\n  }\n\n  switch (kty) {\n    case 'EC': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n      }\n\n      if (use === 'sig' || use === undefined) {\n        switch (crv) {\n          case 'P-256':\n          case 'P-384':\n            algs = algs.concat([`ES${crv.slice(-3)}`]);\n            break;\n          case 'P-521':\n            algs = algs.concat(['ES512']);\n            break;\n          case 'secp256k1':\n            if (jose.cryptoRuntime === 'node:crypto') {\n              algs = algs.concat(['ES256K']);\n            }\n            break;\n        }\n      }\n\n      return new Set(algs);\n    }\n    case 'OKP': {\n      return new Set(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n    }\n    case 'RSA': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['RSA-OAEP', 'RSA-OAEP-256', 'RSA-OAEP-384', 'RSA-OAEP-512']);\n        if (jose.cryptoRuntime === 'node:crypto') {\n          algs = algs.concat(['RSA1_5']);\n        }\n      }\n\n      if (use === 'sig' || use === undefined) {\n        algs = algs.concat(['PS256', 'PS384', 'PS512', 'RS256', 'RS384', 'RS512']);\n      }\n\n      return new Set(algs);\n    }\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nmodule.exports = class KeyStore {\n  #keys;\n\n  constructor(i, keys) {\n    if (i !== internal) throw new Error('invalid constructor call');\n    this.#keys = keys;\n  }\n\n  toJWKS() {\n    return {\n      keys: this.map(({ jwk: { d, p, q, dp, dq, qi, ...jwk } }) => jwk),\n    };\n  }\n\n  all({ alg, kid, use } = {}) {\n    if (!use || !alg) {\n      throw new Error();\n    }\n\n    const kty = getKtyFromAlg(alg);\n\n    const search = { alg, use };\n    return this.filter((key) => {\n      let candidate = true;\n\n      if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n        candidate = false;\n      }\n\n      if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n        candidate = false;\n      }\n\n      if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n        candidate = false;\n      }\n\n      if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n        candidate = false;\n      } else if (!key.algorithms.has(alg)) {\n        candidate = false;\n      }\n\n      return candidate;\n    }).sort((first, second) => keyscore(second, search) - keyscore(first, search));\n  }\n\n  get(...args) {\n    return this.all(...args)[0];\n  }\n\n  static async fromJWKS(jwks, { onlyPublic = false, onlyPrivate = false } = {}) {\n    if (\n      !isPlainObject(jwks) ||\n      !Array.isArray(jwks.keys) ||\n      jwks.keys.some((k) => !isPlainObject(k) || !('kty' in k))\n    ) {\n      throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n\n    const keys = [];\n\n    for (let jwk of jwks.keys) {\n      jwk = clone(jwk);\n      const { kty, kid, crv } = jwk;\n\n      let { alg, use } = jwk;\n\n      if (typeof kty !== 'string' || !kty) {\n        continue;\n      }\n\n      if (use !== undefined && use !== 'sig' && use !== 'enc') {\n        continue;\n      }\n\n      if (typeof alg !== 'string' && alg !== undefined) {\n        continue;\n      }\n\n      if (typeof kid !== 'string' && kid !== undefined) {\n        continue;\n      }\n\n      if (kty === 'EC' && use === 'sig') {\n        switch (crv) {\n          case 'P-256':\n            alg = 'ES256';\n            break;\n          case 'P-384':\n            alg = 'ES384';\n            break;\n          case 'P-521':\n            alg = 'ES512';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (crv === 'secp256k1') {\n        use = 'sig';\n        alg = 'ES256K';\n      }\n\n      if (kty === 'OKP') {\n        switch (crv) {\n          case 'Ed25519':\n          case 'Ed448':\n            use = 'sig';\n            alg = 'EdDSA';\n            break;\n          case 'X25519':\n          case 'X448':\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (alg && !use) {\n        switch (true) {\n          case alg.startsWith('ECDH'):\n            use = 'enc';\n            break;\n          case alg.startsWith('RSA'):\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (onlyPrivate && (jwk.kty === 'oct' || !jwk.d)) {\n        throw new Error('jwks must only contain private keys');\n      }\n\n      if (onlyPublic && (jwk.d || jwk.k)) {\n        continue;\n      }\n\n      keys.push({\n        jwk: { ...jwk, alg, use },\n        async keyObject(alg) {\n          if (this[alg]) {\n            return this[alg];\n          }\n\n          const keyObject = await jose.importJWK(this.jwk, alg);\n          this[alg] = keyObject;\n          return keyObject;\n        },\n        get algorithms() {\n          Object.defineProperty(this, 'algorithms', {\n            value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n            enumerable: true,\n            configurable: false,\n          });\n          return this.algorithms;\n        },\n      });\n    }\n\n    return new this(internal, keys);\n  }\n\n  filter(...args) {\n    return this.#keys.filter(...args);\n  }\n\n  find(...args) {\n    return this.#keys.find(...args);\n  }\n\n  every(...args) {\n    return this.#keys.every(...args);\n  }\n\n  some(...args) {\n    return this.#keys.some(...args);\n  }\n\n  map(...args) {\n    return this.#keys.map(...args);\n  }\n\n  forEach(...args) {\n    return this.#keys.forEach(...args);\n  }\n\n  reduce(...args) {\n    return this.#keys.reduce(...args);\n  }\n\n  sort(...args) {\n    return this.#keys.sort(...args);\n  }\n\n  *[Symbol.iterator]() {\n    for (const key of this.#keys) {\n      yield key;\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/merge.js":
/*!*********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/merge.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction merge(target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (isPlainObject(target[key]) && isPlainObject(value)) {\n        target[key] = merge(target[key], value);\n      } else if (typeof value !== 'undefined') {\n        target[key] = value;\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = merge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hZ2FyYWp1XFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFZvaWNlYmlyZCBuZXdzXFxmbGlwLW5ld3NcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxtZXJnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1BsYWluT2JqZWN0ID0gcmVxdWlyZSgnLi9pc19wbGFpbl9vYmplY3QnKTtcblxuZnVuY3Rpb24gbWVyZ2UodGFyZ2V0LCAuLi5zb3VyY2VzKSB7XG4gIGZvciAoY29uc3Qgc291cmNlIG9mIHNvdXJjZXMpIHtcbiAgICBpZiAoIWlzUGxhaW5PYmplY3Qoc291cmNlKSkge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHNvdXJjZSkpIHtcbiAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBpZiAqL1xuICAgICAgaWYgKGtleSA9PT0gJ19fcHJvdG9fXycgfHwga2V5ID09PSAnY29uc3RydWN0b3InKSB7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuICAgICAgaWYgKGlzUGxhaW5PYmplY3QodGFyZ2V0W2tleV0pICYmIGlzUGxhaW5PYmplY3QodmFsdWUpKSB7XG4gICAgICAgIHRhcmdldFtrZXldID0gbWVyZ2UodGFyZ2V0W2tleV0sIHZhbHVlKTtcbiAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICB0YXJnZXRba2V5XSA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB0YXJnZXQ7XG59XG5cbm1vZHVsZS5leHBvcnRzID0gbWVyZ2U7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/pick.js":
/*!********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/pick.js ***!
  \********************************************************/
/***/ ((module) => {

eval("module.exports = function pick(object, ...paths) {\n  const obj = {};\n  for (const path of paths) {\n    if (object[path] !== undefined) {\n      obj[path] = object[path];\n    }\n  }\n  return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9waWNrLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWdhcmFqdVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxWb2ljZWJpcmQgbmV3c1xcZmxpcC1uZXdzXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xccGljay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIHBpY2sob2JqZWN0LCAuLi5wYXRocykge1xuICBjb25zdCBvYmogPSB7fTtcbiAgZm9yIChjb25zdCBwYXRoIG9mIHBhdGhzKSB7XG4gICAgaWYgKG9iamVjdFtwYXRoXSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBvYmpbcGF0aF0gPSBvYmplY3RbcGF0aF07XG4gICAgfVxuICB9XG4gIHJldHVybiBvYmo7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/process_response.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/process_response.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { STATUS_CODES } = __webpack_require__(/*! http */ \"http\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst { OPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\n\nconst throwAuthenticateErrors = (response) => {\n  const params = parseWwwAuthenticate(response.headers['www-authenticate']);\n\n  if (params.error) {\n    throw new OPError(params, response);\n  }\n};\n\nconst isStandardBodyError = (response) => {\n  let result = false;\n  try {\n    let jsonbody;\n    if (typeof response.body !== 'object' || Buffer.isBuffer(response.body)) {\n      jsonbody = JSON.parse(response.body);\n    } else {\n      jsonbody = response.body;\n    }\n    result = typeof jsonbody.error === 'string' && jsonbody.error.length;\n    if (result) Object.defineProperty(response, 'body', { value: jsonbody, configurable: true });\n  } catch (err) {}\n\n  return result;\n};\n\nfunction processResponse(response, { statusCode = 200, body = true, bearer = false } = {}) {\n  if (response.statusCode !== statusCode) {\n    if (bearer) {\n      throwAuthenticateErrors(response);\n    }\n\n    if (isStandardBodyError(response)) {\n      throw new OPError(response.body, response);\n    }\n\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s, got: %i %s',\n          statusCode,\n          STATUS_CODES[statusCode],\n          response.statusCode,\n          STATUS_CODES[response.statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  if (body && !response.body) {\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s with body but no body was returned',\n          statusCode,\n          STATUS_CODES[statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  return response.body;\n}\n\nmodule.exports = processResponse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/request.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/request.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const assert = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { once } = __webpack_require__(/*! events */ \"events\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\n\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\n\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/openid-client/package.json\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst pick = __webpack_require__(/*! ./pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { deep: defaultsDeep } = __webpack_require__(/*! ./defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst { HTTP_OPTIONS } = __webpack_require__(/*! ./consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\n\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\n\nconst allowed = [\n  'agent',\n  'ca',\n  'cert',\n  'crl',\n  'headers',\n  'key',\n  'lookup',\n  'passphrase',\n  'pfx',\n  'timeout',\n];\n\nconst setDefaults = (props, options) => {\n  DEFAULT_HTTP_OPTIONS = defaultsDeep(\n    {},\n    props.length ? pick(options, ...props) : options,\n    DEFAULT_HTTP_OPTIONS,\n  );\n};\n\nsetDefaults([], {\n  headers: {\n    'User-Agent': `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n    'Accept-Encoding': 'identity',\n  },\n  timeout: 3500,\n});\n\nfunction send(req, body, contentType) {\n  if (contentType) {\n    req.removeHeader('content-type');\n    req.setHeader('content-type', contentType);\n  }\n  if (body) {\n    req.removeHeader('content-length');\n    req.setHeader('content-length', Buffer.byteLength(body));\n    req.write(body);\n  }\n  req.end();\n}\n\nconst nonces = new LRU({ max: 100 });\n\nmodule.exports = async function request(options, { accessToken, mTLS = false, DPoP } = {}) {\n  let url;\n  try {\n    url = new URL(options.url);\n    delete options.url;\n    assert(/^(https?:)$/.test(url.protocol));\n  } catch (err) {\n    throw new TypeError('only valid absolute URLs can be requested');\n  }\n  const optsFn = this[HTTP_OPTIONS];\n  let opts = options;\n\n  const nonceKey = `${url.origin}${url.pathname}`;\n  if (DPoP && 'dpopProof' in this) {\n    opts.headers = opts.headers || {};\n    opts.headers.DPoP = await this.dpopProof(\n      {\n        htu: `${url.origin}${url.pathname}`,\n        htm: options.method || 'GET',\n        nonce: nonces.get(nonceKey),\n      },\n      DPoP,\n      accessToken,\n    );\n  }\n\n  let userOptions;\n  if (optsFn) {\n    userOptions = pick(\n      optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)),\n      ...allowed,\n    );\n  }\n  opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n\n  if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n    throw new TypeError('mutual-TLS certificate and key not set');\n  }\n\n  if (opts.searchParams) {\n    for (const [key, value] of Object.entries(opts.searchParams)) {\n      url.searchParams.delete(key);\n      url.searchParams.set(key, value);\n    }\n  }\n\n  let responseType;\n  let form;\n  let json;\n  let body;\n  ({ form, responseType, json, body, ...opts } = opts);\n\n  for (const [key, value] of Object.entries(opts.headers || {})) {\n    if (value === undefined) {\n      delete opts.headers[key];\n    }\n  }\n\n  let response;\n  const req = (url.protocol === 'https:' ? https.request : http.request)(url.href, opts);\n  return (async () => {\n    if (json) {\n      send(req, JSON.stringify(json), 'application/json');\n    } else if (form) {\n      send(req, querystring.stringify(form), 'application/x-www-form-urlencoded');\n    } else if (body) {\n      send(req, body);\n    } else {\n      send(req);\n    }\n\n    [response] = await Promise.race([once(req, 'response'), once(req, 'timeout')]);\n\n    // timeout reached\n    if (!response) {\n      req.destroy();\n      throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n    }\n\n    const parts = [];\n\n    for await (const part of response) {\n      parts.push(part);\n    }\n\n    if (parts.length) {\n      switch (responseType) {\n        case 'json': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              let value = Buffer.concat(parts);\n              try {\n                value = JSON.parse(value);\n              } catch (err) {\n                Object.defineProperty(err, 'response', { value: response });\n                throw err;\n              } finally {\n                Object.defineProperty(response, 'body', { value, configurable: true });\n              }\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        case undefined:\n        case 'buffer': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              const value = Buffer.concat(parts);\n              Object.defineProperty(response, 'body', { value, configurable: true });\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        default:\n          throw new TypeError('unsupported responseType request option');\n      }\n    }\n\n    return response;\n  })()\n    .catch((err) => {\n      if (response) Object.defineProperty(err, 'response', { value: response });\n      throw err;\n    })\n    .finally(() => {\n      const dpopNonce = response && response.headers['dpop-nonce'];\n      if (dpopNonce && NQCHAR.test(dpopNonce)) {\n        nonces.set(nonceKey, dpopNonce);\n      }\n    });\n};\n\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js":
/*!******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/unix_timestamp.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("module.exports = () => Math.floor(Date.now() / 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy91bml4X3RpbWVzdGFtcC5qcyIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWdhcmFqdVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxWb2ljZWJpcmQgbmV3c1xcZmxpcC1uZXdzXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcdW5peF90aW1lc3RhbXAuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSAoKSA9PiBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/weak_cache.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports.keystores = new WeakMap();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzIiwibWFwcGluZ3MiOiJBQUFBLHdCQUF3QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWdhcmFqdVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxWb2ljZWJpcmQgbmV3c1xcZmxpcC1uZXdzXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcd2Vha19jYWNoZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cy5rZXlzdG9yZXMgPSBuZXcgV2Vha01hcCgpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js":
/*!***********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/webfinger_normalize.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\n\nconst PORT = /^\\d+$/;\n\nfunction hasScheme(input) {\n  if (input.includes('://')) return true;\n\n  const authority = input.replace(/(\\/|\\?)/g, '#').split('#')[0];\n  if (authority.includes(':')) {\n    const index = authority.indexOf(':');\n    const hostOrPort = authority.slice(index + 1);\n    if (!PORT.test(hostOrPort)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction acctSchemeAssumed(input) {\n  if (!input.includes('@')) return false;\n  const parts = input.split('@');\n  const host = parts[parts.length - 1];\n  return !(host.includes(':') || host.includes('/') || host.includes('?'));\n}\n\nfunction normalize(input) {\n  if (typeof input !== 'string') {\n    throw new TypeError('input must be a string');\n  }\n\n  let output;\n  if (hasScheme(input)) {\n    output = input;\n  } else if (acctSchemeAssumed(input)) {\n    output = `acct:${input}`;\n  } else {\n    output = `https://${input}`;\n  }\n\n  return output.split('#')[0];\n}\n\nmodule.exports = normalize;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWJmaW5nZXJfbm9ybWFsaXplLmpzIiwibWFwcGluZ3MiOiJBQUFBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixxQkFBcUIsTUFBTTtBQUMzQixJQUFJO0FBQ0osd0JBQXdCLE1BQU07QUFDOUI7O0FBRUE7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWdhcmFqdVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxWb2ljZWJpcmQgbmV3c1xcZmxpcC1uZXdzXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcd2ViZmluZ2VyX25vcm1hbGl6ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDcmVkaXQ6IGh0dHBzOi8vZ2l0aHViLmNvbS9yb2hlL3B5b2lkYy9ibG9iL21hc3Rlci9zcmMvb2ljL3V0aWxzL3dlYmZpbmdlci5weVxuXG4vLyAtLSBOb3JtYWxpemF0aW9uIC0tXG4vLyBBIHN0cmluZyBvZiBhbnkgb3RoZXIgdHlwZSBpcyBpbnRlcnByZXRlZCBhcyBhIFVSSSBlaXRoZXIgdGhlIGZvcm0gb2Ygc2NoZW1lXG4vLyBcIjovL1wiIGF1dGhvcml0eSBwYXRoLWFiZW1wdHkgWyBcIj9cIiBxdWVyeSBdIFsgXCIjXCIgZnJhZ21lbnQgXSBvciBhdXRob3JpdHlcbi8vIHBhdGgtYWJlbXB0eSBbIFwiP1wiIHF1ZXJ5IF0gWyBcIiNcIiBmcmFnbWVudCBdIHBlciBSRkMgMzk4NiBbUkZDMzk4Nl0gYW5kIGlzXG4vLyBub3JtYWxpemVkIGFjY29yZGluZyB0byB0aGUgZm9sbG93aW5nIHJ1bGVzOlxuLy9cbi8vIElmIHRoZSB1c2VyIGlucHV0IElkZW50aWZpZXIgZG9lcyBub3QgaGF2ZSBhbiBSRkMgMzk4NiBbUkZDMzk4Nl0gc2NoZW1lXG4vLyBwb3J0aW9uLCB0aGUgc3RyaW5nIGlzIGludGVycHJldGVkIGFzIFt1c2VyaW5mbyBcIkBcIl0gaG9zdCBbXCI6XCIgcG9ydF1cbi8vIHBhdGgtYWJlbXB0eSBbIFwiP1wiIHF1ZXJ5IF0gWyBcIiNcIiBmcmFnbWVudCBdIHBlciBSRkMgMzk4NiBbUkZDMzk4Nl0uXG4vLyBJZiB0aGUgdXNlcmluZm8gY29tcG9uZW50IGlzIHByZXNlbnQgYW5kIGFsbCBvZiB0aGUgcGF0aCBjb21wb25lbnQsIHF1ZXJ5XG4vLyBjb21wb25lbnQsIGFuZCBwb3J0IGNvbXBvbmVudCBhcmUgZW1wdHksIHRoZSBhY2N0IHNjaGVtZSBpcyBhc3N1bWVkLiBJbiB0aGlzXG4vLyBjYXNlLCB0aGUgbm9ybWFsaXplZCBVUkkgaXMgZm9ybWVkIGJ5IHByZWZpeGluZyBhY2N0OiB0byB0aGUgc3RyaW5nIGFzIHRoZVxuLy8gc2NoZW1lLiBQZXIgdGhlICdhY2N0JyBVUkkgU2NoZW1lIFtJ4oCRRC5pZXRm4oCRYXBwc2F3Z+KAkWFjY3TigJF1cmldLCBpZiB0aGVyZSBpcyBhblxuLy8gYXQtc2lnbiBjaGFyYWN0ZXIgKCdAJykgaW4gdGhlIHVzZXJpbmZvIGNvbXBvbmVudCwgaXQgbmVlZHMgdG8gYmVcbi8vIHBlcmNlbnQtZW5jb2RlZCBhcyBkZXNjcmliZWQgaW4gUkZDIDM5ODYgW1JGQzM5ODZdLlxuLy8gRm9yIGFsbCBvdGhlciBpbnB1dHMgd2l0aG91dCBhIHNjaGVtZSBwb3J0aW9uLCB0aGUgaHR0cHMgc2NoZW1lIGlzIGFzc3VtZWQsXG4vLyBhbmQgdGhlIG5vcm1hbGl6ZWQgVVJJIGlzIGZvcm1lZCBieSBwcmVmaXhpbmcgaHR0cHM6Ly8gdG8gdGhlIHN0cmluZyBhcyB0aGVcbi8vIHNjaGVtZS5cbi8vIElmIHRoZSByZXN1bHRpbmcgVVJJIGNvbnRhaW5zIGEgZnJhZ21lbnQgcG9ydGlvbiwgaXQgTVVTVCBiZSBzdHJpcHBlZCBvZmZcbi8vIHRvZ2V0aGVyIHdpdGggdGhlIGZyYWdtZW50IGRlbGltaXRlciBjaGFyYWN0ZXIgXCIjXCIuXG4vLyBUaGUgV2ViRmluZ2VyIFtJ4oCRRC5pZXRm4oCRYXBwc2F3Z+KAkXdlYmZpbmdlcl0gUmVzb3VyY2UgaW4gdGhpcyBjYXNlIGlzIHRoZVxuLy8gcmVzdWx0aW5nIFVSSSwgYW5kIHRoZSBXZWJGaW5nZXIgSG9zdCBpcyB0aGUgYXV0aG9yaXR5IGNvbXBvbmVudC5cbi8vXG4vLyBOb3RlOiBTaW5jZSB0aGUgZGVmaW5pdGlvbiBvZiBhdXRob3JpdHkgaW4gUkZDIDM5ODYgW1JGQzM5ODZdIGlzXG4vLyBbIHVzZXJpbmZvIFwiQFwiIF0gaG9zdCBbIFwiOlwiIHBvcnQgXSwgaXQgaXMgbGVnYWwgdG8gaGF2ZSBhIHVzZXIgaW5wdXRcbi8vIGlkZW50aWZpZXIgbGlrZSB1c2VyaW5mb0Bob3N0OnBvcnQsIGUuZy4sIGFsaWNlQGV4YW1wbGUuY29tOjgwODAuXG5cbmNvbnN0IFBPUlQgPSAvXlxcZCskLztcblxuZnVuY3Rpb24gaGFzU2NoZW1lKGlucHV0KSB7XG4gIGlmIChpbnB1dC5pbmNsdWRlcygnOi8vJykpIHJldHVybiB0cnVlO1xuXG4gIGNvbnN0IGF1dGhvcml0eSA9IGlucHV0LnJlcGxhY2UoLyhcXC98XFw/KS9nLCAnIycpLnNwbGl0KCcjJylbMF07XG4gIGlmIChhdXRob3JpdHkuaW5jbHVkZXMoJzonKSkge1xuICAgIGNvbnN0IGluZGV4ID0gYXV0aG9yaXR5LmluZGV4T2YoJzonKTtcbiAgICBjb25zdCBob3N0T3JQb3J0ID0gYXV0aG9yaXR5LnNsaWNlKGluZGV4ICsgMSk7XG4gICAgaWYgKCFQT1JULnRlc3QoaG9zdE9yUG9ydCkpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZTtcbn1cblxuZnVuY3Rpb24gYWNjdFNjaGVtZUFzc3VtZWQoaW5wdXQpIHtcbiAgaWYgKCFpbnB1dC5pbmNsdWRlcygnQCcpKSByZXR1cm4gZmFsc2U7XG4gIGNvbnN0IHBhcnRzID0gaW5wdXQuc3BsaXQoJ0AnKTtcbiAgY29uc3QgaG9zdCA9IHBhcnRzW3BhcnRzLmxlbmd0aCAtIDFdO1xuICByZXR1cm4gIShob3N0LmluY2x1ZGVzKCc6JykgfHwgaG9zdC5pbmNsdWRlcygnLycpIHx8IGhvc3QuaW5jbHVkZXMoJz8nKSk7XG59XG5cbmZ1bmN0aW9uIG5vcm1hbGl6ZShpbnB1dCkge1xuICBpZiAodHlwZW9mIGlucHV0ICE9PSAnc3RyaW5nJykge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2lucHV0IG11c3QgYmUgYSBzdHJpbmcnKTtcbiAgfVxuXG4gIGxldCBvdXRwdXQ7XG4gIGlmIChoYXNTY2hlbWUoaW5wdXQpKSB7XG4gICAgb3V0cHV0ID0gaW5wdXQ7XG4gIH0gZWxzZSBpZiAoYWNjdFNjaGVtZUFzc3VtZWQoaW5wdXQpKSB7XG4gICAgb3V0cHV0ID0gYGFjY3Q6JHtpbnB1dH1gO1xuICB9IGVsc2Uge1xuICAgIG91dHB1dCA9IGBodHRwczovLyR7aW5wdXR9YDtcbiAgfVxuXG4gIHJldHVybiBvdXRwdXQuc3BsaXQoJyMnKVswXTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBub3JtYWxpemU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js":
/*!***************************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/www_authenticate_parser.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("const REGEXP = /(\\w+)=(\"[^\"]*\")/g;\n\nmodule.exports = (wwwAuthenticate) => {\n  const params = {};\n  try {\n    while (REGEXP.exec(wwwAuthenticate) !== null) {\n      if (RegExp.$1 && RegExp.$2) {\n        params[RegExp.$1] = RegExp.$2.slice(1, -1);\n      }\n    }\n  } catch (err) {}\n\n  return params;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7QUFFSjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hZ2FyYWp1XFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFZvaWNlYmlyZCBuZXdzXFxmbGlwLW5ld3NcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFx3d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBSRUdFWFAgPSAvKFxcdyspPShcIlteXCJdKlwiKS9nO1xuXG5tb2R1bGUuZXhwb3J0cyA9ICh3d3dBdXRoZW50aWNhdGUpID0+IHtcbiAgY29uc3QgcGFyYW1zID0ge307XG4gIHRyeSB7XG4gICAgd2hpbGUgKFJFR0VYUC5leGVjKHd3d0F1dGhlbnRpY2F0ZSkgIT09IG51bGwpIHtcbiAgICAgIGlmIChSZWdFeHAuJDEgJiYgUmVnRXhwLiQyKSB7XG4gICAgICAgIHBhcmFtc1tSZWdFeHAuJDFdID0gUmVnRXhwLiQyLnNsaWNlKDEsIC0xKTtcbiAgICAgIH1cbiAgICB9XG4gIH0gY2F0Y2ggKGVycikge31cblxuICByZXR1cm4gcGFyYW1zO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/lib/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Issuer = __webpack_require__(/*! ./issuer */ \"(rsc)/./node_modules/openid-client/lib/issuer.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst Strategy = __webpack_require__(/*! ./passport_strategy */ \"(rsc)/./node_modules/openid-client/lib/passport_strategy.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { CLOCK_TOLERANCE, HTTP_OPTIONS } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst generators = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst { setDefaults } = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nmodule.exports = {\n  Issuer,\n  Strategy,\n  TokenSet,\n  errors: {\n    OPError,\n    RPError,\n  },\n  custom: {\n    setHttpOptionsDefaults: setDefaults,\n    http_options: HTTP_OPTIONS,\n    clock_tolerance: CLOCK_TOLERANCE,\n  },\n  generators,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxtQkFBTyxDQUFDLGtFQUFVO0FBQ2pDLFFBQVEsbUJBQW1CLEVBQUUsbUJBQU8sQ0FBQyxrRUFBVTtBQUMvQyxpQkFBaUIsbUJBQU8sQ0FBQyx3RkFBcUI7QUFDOUMsaUJBQWlCLG1CQUFPLENBQUMsd0VBQWE7QUFDdEMsUUFBUSxnQ0FBZ0MsRUFBRSxtQkFBTyxDQUFDLGtGQUFrQjtBQUNwRSxtQkFBbUIsbUJBQU8sQ0FBQywwRkFBc0I7QUFDakQsUUFBUSxjQUFjLEVBQUUsbUJBQU8sQ0FBQyxvRkFBbUI7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFnYXJhanVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVm9pY2ViaXJkIG5ld3NcXGZsaXAtbmV3c1xcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IElzc3VlciA9IHJlcXVpcmUoJy4vaXNzdWVyJyk7XG5jb25zdCB7IE9QRXJyb3IsIFJQRXJyb3IgfSA9IHJlcXVpcmUoJy4vZXJyb3JzJyk7XG5jb25zdCBTdHJhdGVneSA9IHJlcXVpcmUoJy4vcGFzc3BvcnRfc3RyYXRlZ3knKTtcbmNvbnN0IFRva2VuU2V0ID0gcmVxdWlyZSgnLi90b2tlbl9zZXQnKTtcbmNvbnN0IHsgQ0xPQ0tfVE9MRVJBTkNFLCBIVFRQX09QVElPTlMgfSA9IHJlcXVpcmUoJy4vaGVscGVycy9jb25zdHMnKTtcbmNvbnN0IGdlbmVyYXRvcnMgPSByZXF1aXJlKCcuL2hlbHBlcnMvZ2VuZXJhdG9ycycpO1xuY29uc3QgeyBzZXREZWZhdWx0cyB9ID0gcmVxdWlyZSgnLi9oZWxwZXJzL3JlcXVlc3QnKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIElzc3VlcixcbiAgU3RyYXRlZ3ksXG4gIFRva2VuU2V0LFxuICBlcnJvcnM6IHtcbiAgICBPUEVycm9yLFxuICAgIFJQRXJyb3IsXG4gIH0sXG4gIGN1c3RvbToge1xuICAgIHNldEh0dHBPcHRpb25zRGVmYXVsdHM6IHNldERlZmF1bHRzLFxuICAgIGh0dHBfb3B0aW9uczogSFRUUF9PUFRJT05TLFxuICAgIGNsb2NrX3RvbGVyYW5jZTogQ0xPQ0tfVE9MRVJBTkNFLFxuICB9LFxuICBnZW5lcmF0b3JzLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst url = __webpack_require__(/*! url */ \"url\");\n\nconst { RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst getClient = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst registry = __webpack_require__(/*! ./issuer_registry */ \"(rsc)/./node_modules/openid-client/lib/issuer_registry.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst webfingerNormalize = __webpack_require__(/*! ./helpers/webfinger_normalize */ \"(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { keystore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\n\nconst AAD_MULTITENANT_DISCOVERY = [\n  'https://login.microsoftonline.com/common/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration',\n];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n  claim_types_supported: ['normal'],\n  claims_parameter_supported: false,\n  grant_types_supported: ['authorization_code', 'implicit'],\n  request_parameter_supported: false,\n  request_uri_parameter_supported: true,\n  require_request_uri_registration: false,\n  response_modes_supported: ['query', 'fragment'],\n  token_endpoint_auth_methods_supported: ['client_secret_basic'],\n};\n\nclass Issuer {\n  #metadata;\n  constructor(meta = {}) {\n    const aadIssValidation = meta[AAD_MULTITENANT];\n    delete meta[AAD_MULTITENANT];\n    ['introspection', 'revocation'].forEach((endpoint) => {\n      // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n      // are defined\n      if (\n        meta[`${endpoint}_endpoint`] &&\n        meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined &&\n        meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined\n      ) {\n        if (meta.token_endpoint_auth_methods_supported) {\n          meta[`${endpoint}_endpoint_auth_methods_supported`] =\n            meta.token_endpoint_auth_methods_supported;\n        }\n        if (meta.token_endpoint_auth_signing_alg_values_supported) {\n          meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] =\n            meta.token_endpoint_auth_signing_alg_values_supported;\n        }\n      }\n    });\n\n    this.#metadata = new Map();\n\n    Object.entries(meta).forEach(([key, value]) => {\n      this.#metadata.set(key, value);\n      if (!this[key]) {\n        Object.defineProperty(this, key, {\n          get() {\n            return this.#metadata.get(key);\n          },\n          enumerable: true,\n        });\n      }\n    });\n\n    registry.set(this.issuer, this);\n\n    const Client = getClient(this, aadIssValidation);\n\n    Object.defineProperties(this, {\n      Client: { value: Client, enumerable: true },\n      FAPI1Client: { value: class FAPI1Client extends Client {}, enumerable: true },\n      FAPI2Client: { value: class FAPI2Client extends Client {}, enumerable: true },\n    });\n  }\n\n  get metadata() {\n    return clone(Object.fromEntries(this.#metadata.entries()));\n  }\n\n  static async webfinger(input) {\n    const resource = webfingerNormalize(input);\n    const { host } = url.parse(resource);\n    const webfingerUrl = `https://${host}/.well-known/webfinger`;\n\n    const response = await request.call(this, {\n      method: 'GET',\n      url: webfingerUrl,\n      responseType: 'json',\n      searchParams: { resource, rel: 'http://openid.net/specs/connect/1.0/issuer' },\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n\n    const location =\n      Array.isArray(body.links) &&\n      body.links.find(\n        (link) =>\n          typeof link === 'object' &&\n          link.rel === 'http://openid.net/specs/connect/1.0/issuer' &&\n          link.href,\n      );\n\n    if (!location) {\n      throw new RPError({\n        message: 'no issuer found in webfinger response',\n        body,\n      });\n    }\n\n    if (typeof location.href !== 'string' || !location.href.startsWith('https://')) {\n      throw new RPError({\n        printf: ['invalid issuer location %s', location.href],\n        body,\n      });\n    }\n\n    const expectedIssuer = location.href;\n    if (registry.has(expectedIssuer)) {\n      return registry.get(expectedIssuer);\n    }\n\n    const issuer = await this.discover(expectedIssuer);\n\n    if (issuer.issuer !== expectedIssuer) {\n      registry.del(issuer.issuer);\n      throw new RPError(\n        'discovered issuer mismatch, expected %s, got: %s',\n        expectedIssuer,\n        issuer.issuer,\n      );\n    }\n    return issuer;\n  }\n\n  static async discover(uri) {\n    const wellKnownUri = resolveWellKnownUri(uri);\n\n    const response = await request.call(this, {\n      method: 'GET',\n      responseType: 'json',\n      url: wellKnownUri,\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n    return new Issuer({\n      ...ISSUER_DEFAULTS,\n      ...body,\n      [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find((discoveryURL) =>\n        wellKnownUri.startsWith(discoveryURL),\n      ),\n    });\n  }\n\n  async reloadJwksUri() {\n    await keystore.call(this, true);\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.metadata, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nfunction resolveWellKnownUri(uri) {\n  const parsed = url.parse(uri);\n  if (parsed.pathname.includes('/.well-known/')) {\n    return uri;\n  } else {\n    let pathname;\n    if (parsed.pathname.endsWith('/')) {\n      pathname = `${parsed.pathname}.well-known/openid-configuration`;\n    } else {\n      pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n    }\n    return url.format({ ...parsed, pathname });\n  }\n}\n\nmodule.exports = Issuer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer_registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer_registry.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\n\nmodule.exports = new LRU({ max: 100 });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiJBQUFBLFlBQVksbUJBQU8sQ0FBQywwREFBVzs7QUFFL0IsMkJBQTJCLFVBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFnYXJhanVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVm9pY2ViaXJkIG5ld3NcXGZsaXAtbmV3c1xcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGlzc3Vlcl9yZWdpc3RyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBMUlUgPSByZXF1aXJlKCdscnUtY2FjaGUnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBuZXcgTFJVKHsgbWF4OiAxMDAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer_registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/passport_strategy.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/passport_strategy.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const url = __webpack_require__(/*! url */ \"url\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst cloneDeep = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { BaseClient } = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst { random, codeChallenge } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\n\nfunction verified(err, user, info = {}) {\n  if (err) {\n    this.error(err);\n  } else if (!user) {\n    this.fail(info);\n  } else {\n    this.success(user, info);\n  }\n}\n\nfunction OpenIDConnectStrategy(\n  { client, params = {}, passReqToCallback = false, sessionKey, usePKCE = true, extras = {} } = {},\n  verify,\n) {\n  if (!(client instanceof BaseClient)) {\n    throw new TypeError('client must be an instance of openid-client Client');\n  }\n\n  if (typeof verify !== 'function') {\n    throw new TypeError('verify callback must be a function');\n  }\n\n  if (!client.issuer || !client.issuer.issuer) {\n    throw new TypeError('client must have an issuer with an identifier');\n  }\n\n  this._client = client;\n  this._issuer = client.issuer;\n  this._verify = verify;\n  this._passReqToCallback = passReqToCallback;\n  this._usePKCE = usePKCE;\n  this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n  this._params = cloneDeep(params);\n\n  // state and nonce are handled in authenticate()\n  delete this._params.state;\n  delete this._params.nonce;\n\n  this._extras = cloneDeep(extras);\n\n  if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n  if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n  if (!this._params.scope) this._params.scope = 'openid';\n\n  if (this._usePKCE === true) {\n    const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported)\n      ? this._issuer.code_challenge_methods_supported\n      : false;\n\n    if (supportedMethods && supportedMethods.includes('S256')) {\n      this._usePKCE = 'S256';\n    } else if (supportedMethods && supportedMethods.includes('plain')) {\n      this._usePKCE = 'plain';\n    } else if (supportedMethods) {\n      throw new TypeError(\n        'neither code_challenge_method supported by the client is supported by the issuer',\n      );\n    } else {\n      this._usePKCE = 'S256';\n    }\n  } else if (typeof this._usePKCE === 'string' && !['plain', 'S256'].includes(this._usePKCE)) {\n    throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n  }\n\n  this.name = url.parse(client.issuer.issuer).hostname;\n}\n\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n  (async () => {\n    const client = this._client;\n    if (!req.session) {\n      throw new TypeError('authentication requires session support');\n    }\n    const reqParams = client.callbackParams(req);\n    const sessionKey = this._key;\n\n    const { 0: parameter, length } = Object.keys(reqParams);\n\n    /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */\n    if (length === 0 || (length === 1 && parameter === 'iss')) {\n      // provide options object with extra authentication parameters\n      const params = {\n        state: random(),\n        ...this._params,\n        ...options,\n      };\n\n      if (!params.nonce && params.response_type.includes('id_token')) {\n        params.nonce = random();\n      }\n\n      req.session[sessionKey] = pick(params, 'nonce', 'state', 'max_age', 'response_type');\n\n      if (this._usePKCE && params.response_type.includes('code')) {\n        const verifier = random();\n        req.session[sessionKey].code_verifier = verifier;\n\n        switch (this._usePKCE) {\n          case 'S256':\n            params.code_challenge = codeChallenge(verifier);\n            params.code_challenge_method = 'S256';\n            break;\n          case 'plain':\n            params.code_challenge = verifier;\n            break;\n        }\n      }\n\n      this.redirect(client.authorizationUrl(params));\n      return;\n    }\n    /* end authentication request */\n\n    /* start authentication response */\n\n    const session = req.session[sessionKey];\n    if (Object.keys(session || {}).length === 0) {\n      throw new Error(\n        format(\n          'did not find expected authorization request details in session, req.session[\"%s\"] is %j',\n          sessionKey,\n          session,\n        ),\n      );\n    }\n\n    const {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    } = session;\n\n    try {\n      delete req.session[sessionKey];\n    } catch (err) {}\n\n    const opts = {\n      redirect_uri: this._params.redirect_uri,\n      ...options,\n    };\n\n    const checks = {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    };\n\n    const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n\n    const passReq = this._passReqToCallback;\n    const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n\n    const args = [tokenset, verified.bind(this)];\n\n    if (loadUserinfo) {\n      if (!tokenset.access_token) {\n        throw new RPError({\n          message:\n            'expected access_token to be returned when asking for userinfo in verify callback',\n          tokenset,\n        });\n      }\n      const userinfo = await client.userinfo(tokenset);\n      args.splice(1, 0, userinfo);\n    }\n\n    if (passReq) {\n      args.unshift(req);\n    }\n\n    this._verify(...args);\n    /* end authentication response */\n  })().catch((error) => {\n    if (\n      (error instanceof OPError &&\n        error.error !== 'server_error' &&\n        !error.error.startsWith('invalid')) ||\n      error instanceof RPError\n    ) {\n      this.fail(error);\n    } else {\n      this.error(error);\n    }\n  });\n};\n\nmodule.exports = OpenIDConnectStrategy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/passport_strategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/token_set.js":
/*!*****************************************************!*\
  !*** ./node_modules/openid-client/lib/token_set.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass TokenSet {\n  constructor(values) {\n    Object.assign(this, values);\n    const { constructor, ...properties } = Object.getOwnPropertyDescriptors(\n      this.constructor.prototype,\n    );\n\n    Object.defineProperties(this, properties);\n  }\n\n  set expires_in(value) {\n    this.expires_at = now() + Number(value);\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  claims() {\n    if (!this.id_token) {\n      throw new TypeError('id_token not present in TokenSet');\n    }\n\n    return JSON.parse(base64url.decode(this.id_token.split('.')[1]));\n  }\n}\n\nmodule.exports = TokenSet;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvdG9rZW5fc2V0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLHdGQUFxQjtBQUMvQyxZQUFZLG1CQUFPLENBQUMsa0dBQTBCOztBQUU5QztBQUNBO0FBQ0E7QUFDQSxZQUFZLDZCQUE2QjtBQUN6QztBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hZ2FyYWp1XFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFZvaWNlYmlyZCBuZXdzXFxmbGlwLW5ld3NcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFx0b2tlbl9zZXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgYmFzZTY0dXJsID0gcmVxdWlyZSgnLi9oZWxwZXJzL2Jhc2U2NHVybCcpO1xuY29uc3Qgbm93ID0gcmVxdWlyZSgnLi9oZWxwZXJzL3VuaXhfdGltZXN0YW1wJyk7XG5cbmNsYXNzIFRva2VuU2V0IHtcbiAgY29uc3RydWN0b3IodmFsdWVzKSB7XG4gICAgT2JqZWN0LmFzc2lnbih0aGlzLCB2YWx1ZXMpO1xuICAgIGNvbnN0IHsgY29uc3RydWN0b3IsIC4uLnByb3BlcnRpZXMgfSA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzKFxuICAgICAgdGhpcy5jb25zdHJ1Y3Rvci5wcm90b3R5cGUsXG4gICAgKTtcblxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKHRoaXMsIHByb3BlcnRpZXMpO1xuICB9XG5cbiAgc2V0IGV4cGlyZXNfaW4odmFsdWUpIHtcbiAgICB0aGlzLmV4cGlyZXNfYXQgPSBub3coKSArIE51bWJlcih2YWx1ZSk7XG4gIH1cblxuICBnZXQgZXhwaXJlc19pbigpIHtcbiAgICByZXR1cm4gTWF0aC5tYXguYXBwbHkobnVsbCwgW3RoaXMuZXhwaXJlc19hdCAtIG5vdygpLCAwXSk7XG4gIH1cblxuICBleHBpcmVkKCkge1xuICAgIHJldHVybiB0aGlzLmV4cGlyZXNfaW4gPT09IDA7XG4gIH1cblxuICBjbGFpbXMoKSB7XG4gICAgaWYgKCF0aGlzLmlkX3Rva2VuKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdpZF90b2tlbiBub3QgcHJlc2VudCBpbiBUb2tlblNldCcpO1xuICAgIH1cblxuICAgIHJldHVybiBKU09OLnBhcnNlKGJhc2U2NHVybC5kZWNvZGUodGhpcy5pZF90b2tlbi5zcGxpdCgnLicpWzFdKSk7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBUb2tlblNldDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/token_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/package.json":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/package.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}');

/***/ })

};
;
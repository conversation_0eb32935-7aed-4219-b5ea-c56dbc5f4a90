"use client"

import Image from "next/image"

interface AdCardProps {
  ad: {
    id: string
    title: string
    image: string
    link: string
  }
}

export function AdCard({ ad }: AdCardProps) {
  const handleClick = async () => {
    try {
      // Track ad click
      await fetch(`/api/ads/${ad.id}/click`, {
        method: "POST"
      })
    } catch (error) {
      console.error("Failed to track ad click:", error)
    }

    // Open link
    if (ad.link && ad.link !== "#") {
      window.open(ad.link, "_blank", "noopener,noreferrer")
    }
  }

  return (
    <div 
      className="bg-gradient-to-r from-yellow-100 to-yellow-200 rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow border-2 border-yellow-300"
      onClick={handleClick}
    >
      <div className="relative h-48">
        <Image
          src={ad.image}
          alt={ad.title}
          fill
          className="object-cover"
        />
        <div className="absolute top-2 right-2">
          <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
            Ad
          </span>
        </div>
      </div>
      
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 text-sm">
          {ad.title}
        </h3>
        <p className="text-xs text-gray-600 mt-1">
          Sponsored Content
        </p>
      </div>
    </div>
  )
}

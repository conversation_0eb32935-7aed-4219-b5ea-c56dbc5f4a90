import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const news = await prisma.news.findUnique({
      where: { id: params.id }
    })

    if (!news) {
      return NextResponse.json(
        { error: "News not found" },
        { status: 404 }
      )
    }

    // Increment like count
    const updatedNews = await prisma.news.update({
      where: { id: params.id },
      data: {
        likes: {
          increment: 1
        }
      }
    })

    return NextResponse.json({ 
      success: true, 
      likes: updatedNews.likes 
    })
  } catch (error) {
    console.error("Failed to like news:", error)
    return NextResponse.json(
      { error: "Failed to like news" },
      { status: 500 }
    )
  }
}

import { NewsGrid } from "@/components/news-grid"
import { Header } from "@/components/header"
import { AdBanner } from "@/components/ad-banner"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-red-50 to-white">
      <Header />

      {/* Telugu News Style Hero Section */}
      <div className="bg-gradient-to-r from-red-500 to-red-600 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              {[...Array(12)].map((_, i) => (
                <span key={i} className="text-yellow-300 text-lg mx-1">⭐</span>
              ))}
            </div>
            <h1 className="text-4xl font-bold mb-2">
              Flip News
            </h1>
            <p className="text-red-100 text-lg">
              తెలుగు వార్తలు • Telugu News • Breaking Updates
            </p>
          </div>
        </div>
      </div>

      <main className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            తాజా వార్తలు • Latest News
          </h2>
          <p className="text-gray-600">
            అప్‌డేట్‌లతో ఉండండి • Stay updated with breaking news and trending stories
          </p>
        </div>

        <AdBanner />
        <NewsGrid />
      </main>
    </div>
  )
}

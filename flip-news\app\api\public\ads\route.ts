import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const now = new Date()

    const ads = await prisma.ad.findMany({
      where: {
        active: true,
        OR: [
          {
            startDate: null,
            endDate: null
          },
          {
            startDate: {
              lte: now
            },
            endDate: {
              gte: now
            }
          },
          {
            startDate: {
              lte: now
            },
            endDate: null
          },
          {
            startDate: null,
            endDate: {
              gte: now
            }
          }
        ]
      },
      orderBy: {
        createdAt: "desc"
      }
    })

    return NextResponse.json({ ads })
  } catch (error) {
    console.error("Failed to fetch ads:", error)
    return NextResponse.json(
      { error: "Failed to fetch ads" },
      { status: 500 }
    )
  }
}

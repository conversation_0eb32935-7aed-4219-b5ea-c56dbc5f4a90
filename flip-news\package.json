{"name": "flip-news", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@prisma/client": "^6.9.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "html2canvas": "^1.4.1", "lucide-react": "^0.513.0", "next": "15.3.3", "next-auth": "^4.24.11", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}
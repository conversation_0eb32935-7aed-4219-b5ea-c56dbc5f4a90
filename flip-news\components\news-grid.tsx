"use client"

import { useState, useEffect } from "react"
import { NewsCard } from "./news-card"
import { AdCard } from "./ad-card"
import { sampleTeluguNews } from "@/lib/seed-data"

interface NewsItem {
  id: string
  title: string
  excerpt: string
  image: string
  category: string
  createdAt: string
  views: number
  likes: number
  shares: number
  author: {
    name: string
  }
}

interface Ad {
  id: string
  title: string
  image: string
  link: string
}

export function NewsGrid() {
  const [news, setNews] = useState<NewsItem[]>([])
  const [ads, setAds] = useState<Ad[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchNews()
    fetchAds()
  }, [])

  const fetchNews = async () => {
    try {
      const response = await fetch("/api/public/news")
      if (response.ok) {
        const data = await response.json()
        setNews(data.news || [])
      }
    } catch (error) {
      console.error("Failed to fetch news:", error)
      // For demo purposes, show Telugu sample data
      setNews(sampleTeluguNews)
    } finally {
      setLoading(false)
    }
  }

  const fetchAds = async () => {
    try {
      const response = await fetch("/api/public/ads")
      if (response.ok) {
        const data = await response.json()
        setAds(data.ads || [])
      }
    } catch (error) {
      console.error("Failed to fetch ads:", error)
      // For demo purposes, show sample ads
      setAds(sampleAds)
    }
  }

  // Sample data for demo
  const sampleNews: NewsItem[] = [
    {
      id: "1",
      title: "Breaking: Major Technology Breakthrough Announced",
      excerpt: "Scientists have made a significant discovery that could revolutionize the tech industry...",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
      category: "Technology",
      createdAt: new Date().toISOString(),
      views: 1250,
      likes: 89,
      shares: 23,
      author: { name: "Tech Reporter" }
    },
    {
      id: "2",
      title: "Sports Update: Championship Finals This Weekend",
      excerpt: "The most anticipated match of the season is set to take place this weekend...",
      image: "https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=400&h=300&fit=crop",
      category: "Sports",
      createdAt: new Date(Date.now() - 3600000).toISOString(),
      views: 890,
      likes: 67,
      shares: 15,
      author: { name: "Sports Desk" }
    },
    {
      id: "3",
      title: "Economic Markets Show Strong Growth",
      excerpt: "Financial analysts report positive trends in the global markets this quarter...",
      image: "https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400&h=300&fit=crop",
      category: "Business",
      createdAt: new Date(Date.now() - 7200000).toISOString(),
      views: 654,
      likes: 45,
      shares: 12,
      author: { name: "Business Reporter" }
    }
  ]

  const sampleAds: Ad[] = [
    {
      id: "1",
      title: "Special Offer - 50% Off",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=200&fit=crop",
      link: "#"
    }
  ]

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-300 rounded-lg h-48 mb-4"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
          </div>
        ))}
      </div>
    )
  }

  // Insert ads every 3 news items
  const contentItems = []
  let adIndex = 0

  for (let i = 0; i < news.length; i++) {
    contentItems.push({ type: "news", data: news[i] })
    
    // Insert ad every 3 items
    if ((i + 1) % 3 === 0 && adIndex < ads.length) {
      contentItems.push({ type: "ad", data: ads[adIndex] })
      adIndex++
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {contentItems.map((item, index) => (
        <div key={`${item.type}-${index}`}>
          {item.type === "news" ? (
            <NewsCard news={item.data as NewsItem} />
          ) : (
            <AdCard ad={item.data as Ad} />
          )}
        </div>
      ))}
    </div>
  )
}

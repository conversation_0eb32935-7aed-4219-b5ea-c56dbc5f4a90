"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/share-button.tsx":
/*!*************************************!*\
  !*** ./components/share-button.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShareButton: () => (/* binding */ ShareButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ ShareButton auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ShareButton(param) {\n    let { news, onShare } = param;\n    _s();\n    const [isSharing, setIsSharing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const shareUrl = \"\".concat(window.location.origin, \"/news/\").concat(news.id);\n    const shareText = \"Check out this news: \".concat(news.title);\n    const handleShare = async (e)=>{\n        e.stopPropagation();\n        onShare === null || onShare === void 0 ? void 0 : onShare(e);\n        setIsSharing(true);\n        try {\n            // Try Web Share API first\n            if (navigator.share) {\n                await navigator.share({\n                    title: news.title,\n                    text: shareText,\n                    url: shareUrl\n                });\n            } else {\n                // Fallback to screenshot sharing\n                await shareWithScreenshot();\n            }\n        } catch (error) {\n            console.error(\"Share failed:\", error);\n            // Fallback to copy link\n            await copyToClipboard();\n        } finally{\n            setIsSharing(false);\n        }\n    };\n    const shareWithScreenshot = async ()=>{\n        try {\n            const cardElement = document.getElementById(\"news-card-\".concat(news.id));\n            if (!cardElement) return;\n            // Create canvas from the news card\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_2___default()(cardElement, {\n                backgroundColor: \"#ffffff\",\n                scale: 2,\n                logging: false,\n                useCORS: true\n            });\n            // Add watermark and branding\n            const ctx = canvas.getContext(\"2d\");\n            if (ctx) {\n                // Add watermark\n                ctx.fillStyle = \"rgba(0, 0, 0, 0.7)\";\n                ctx.fillRect(0, canvas.height - 60, canvas.width, 60);\n                ctx.fillStyle = \"#ffffff\";\n                ctx.font = \"bold 24px Arial\";\n                ctx.fillText(\"Flip News\", 20, canvas.height - 30);\n                ctx.font = \"16px Arial\";\n                ctx.fillText(shareUrl, 20, canvas.height - 10);\n            }\n            // Convert to blob\n            canvas.toBlob(async (blob)=>{\n                if (!blob) return;\n                try {\n                    // Try to share the image\n                    if (navigator.share && navigator.canShare) {\n                        const file = new File([\n                            blob\n                        ], \"\".concat(news.title.slice(0, 30), \".png\"), {\n                            type: \"image/png\"\n                        });\n                        if (navigator.canShare({\n                            files: [\n                                file\n                            ]\n                        })) {\n                            await navigator.share({\n                                title: news.title,\n                                text: shareText,\n                                files: [\n                                    file\n                                ]\n                            });\n                            return;\n                        }\n                    }\n                    // Fallback: Download the image\n                    const url = URL.createObjectURL(blob);\n                    const a = document.createElement(\"a\");\n                    a.href = url;\n                    a.download = \"\".concat(news.title.slice(0, 30), \".png\");\n                    document.body.appendChild(a);\n                    a.click();\n                    document.body.removeChild(a);\n                    URL.revokeObjectURL(url);\n                    // Also copy the link\n                    await copyToClipboard();\n                } catch (error) {\n                    console.error(\"Screenshot share failed:\", error);\n                    await copyToClipboard();\n                }\n            }, \"image/png\");\n        } catch (error) {\n            console.error(\"Screenshot generation failed:\", error);\n            await copyToClipboard();\n        }\n    };\n    const copyToClipboard = async ()=>{\n        try {\n            await navigator.clipboard.writeText(shareUrl);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (error) {\n            console.error(\"Copy failed:\", error);\n        }\n    };\n    const handleCopyLink = async (e)=>{\n        e.stopPropagation();\n        await copyToClipboard();\n    };\n    const handleDownloadScreenshot = async (e)=>{\n        e.stopPropagation();\n        setIsSharing(true);\n        await shareWithScreenshot();\n        setIsSharing(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-end\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShare,\n                disabled: isSharing,\n                className: \"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md transition-colors disabled:opacity-50 flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Share\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    isSharing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden group-hover:flex items-center space-x-2 ml-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDownloadScreenshot,\n                        disabled: isSharing,\n                        className: \"p-2 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full disabled:opacity-50 transition-colors\",\n                        title: \"Download as image\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopyLink,\n                        className: \"p-2 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full transition-colors\",\n                        title: \"Copy link\",\n                        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\share-button.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n_s(ShareButton, \"QQrzjyYpzko2tOSEzZzp2djBNVk=\");\n_c = ShareButton;\nvar _c;\n$RefreshReg$(_c, \"ShareButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/share-button.tsx\n"));

/***/ })

});
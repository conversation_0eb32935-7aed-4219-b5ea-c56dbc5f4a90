/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/_error"],{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error! ***!
  \************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_error\",\n      function () {\n        return __webpack_require__(/*! next/dist/pages/_error */ \"(pages-dir-browser)/./node_modules/next/dist/pages/_error.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_error\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPW5leHQlMkZkaXN0JTJGcGFnZXMlMkZfZXJyb3ImcGFnZT0lMkZfZXJyb3IhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsNEZBQXdCO0FBQy9DO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL19lcnJvclwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIm5leHQvZGlzdC9wYWdlcy9fZXJyb3JcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL19lcnJvclwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/pages/_error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/pages/_error.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return Error;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _head = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\"));\nconst statusCodes = {\n    400: 'Bad Request',\n    404: 'This page could not be found',\n    405: 'Method Not Allowed',\n    500: 'Internal Server Error'\n};\nfunction _getInitialProps(param) {\n    let { req, res, err } = param;\n    const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n    let hostname;\n    if (true) {\n        hostname = window.location.hostname;\n    } else {}\n    return {\n        statusCode,\n        hostname\n    };\n}\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: '100vh',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center'\n    },\n    desc: {\n        lineHeight: '48px'\n    },\n    h1: {\n        display: 'inline-block',\n        margin: '0 20px 0 0',\n        paddingRight: 23,\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: 'top'\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: '28px'\n    },\n    wrap: {\n        display: 'inline-block'\n    }\n};\nclass Error extends _react.default.Component {\n    render() {\n        const { statusCode, withDarkMode = true } = this.props;\n        const title = this.props.title || statusCodes[statusCode] || 'An unexpected error has occurred';\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            style: styles.error,\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(_head.default, {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"title\", {\n                        children: statusCode ? statusCode + \": \" + title : 'Application error: a client-side exception has occurred'\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    style: styles.desc,\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            dangerouslySetInnerHTML: {\n                                /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */ __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? '@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}' : '')\n                            }\n                        }),\n                        statusCode ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                            className: \"next-error-h1\",\n                            style: styles.h1,\n                            children: statusCode\n                        }) : null,\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            style: styles.wrap,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"h2\", {\n                                style: styles.h2,\n                                children: [\n                                    this.props.title || statusCode ? title : /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                        children: [\n                                            \"Application error: a client-side exception has occurred\",\n                                            ' ',\n                                            Boolean(this.props.hostname) && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                                children: [\n                                                    \"while loading \",\n                                                    this.props.hostname\n                                                ]\n                                            }),\n                                            ' ',\n                                            \"(see the browser console for more information)\"\n                                        ]\n                                    }),\n                                    \".\"\n                                ]\n                            })\n                        })\n                    ]\n                })\n            ]\n        });\n    }\n}\nError.displayName = 'ErrorPage';\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=_error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/pages/_error.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AmpStateContext\", ({\n    enumerable: true,\n    get: function() {\n        return AmpStateContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst AmpStateContext = _react.default.createContext({});\nif (true) {\n    AmpStateContext.displayName = 'AmpStateContext';\n} //# sourceMappingURL=amp-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQUVhQTs7O2VBQUFBOzs7OzRFQUZLO0FBRVgsTUFBTUEsa0JBQXNDQyxPQUFBQSxPQUFLLENBQUNDLGFBQWEsQ0FBQyxDQUFDO0FBRXhFLElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDSCxnQkFBZ0JNLFdBQVcsR0FBRztBQUNoQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWdhcmFqdVxcRG9jdW1lbnRzXFxzcmNcXHNoYXJlZFxcbGliXFxhbXAtY29udGV4dC5zaGFyZWQtcnVudGltZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmV4cG9ydCBjb25zdCBBbXBTdGF0ZUNvbnRleHQ6IFJlYWN0LkNvbnRleHQ8YW55PiA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoe30pXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIEFtcFN0YXRlQ29udGV4dC5kaXNwbGF5TmFtZSA9ICdBbXBTdGF0ZUNvbnRleHQnXG59XG4iXSwibmFtZXMiOlsiQW1wU3RhdGVDb250ZXh0IiwiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-mode.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isInAmpMode\", ({\n    enumerable: true,\n    get: function() {\n        return isInAmpMode;\n    }\n}));\nfunction isInAmpMode(param) {\n    let { ampFirst = false, hybrid = false, hasQuery = false } = param === void 0 ? {} : param;\n    return ampFirst || hybrid && hasQuery;\n} //# sourceMappingURL=amp-mode.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtbW9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7OytDQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsWUFBWTtJQUFBLE1BQzFCQyxXQUFXLEtBQUssRUFDaEJDLFNBQVMsS0FBSyxFQUNkQyxXQUFXLEtBQUssRUFDakIsR0FKMkIsbUJBSXhCLENBQUMsSUFKdUI7SUFLMUIsT0FBT0YsWUFBYUMsVUFBVUM7QUFDaEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFnYXJhanVcXERvY3VtZW50c1xcc3JjXFxzaGFyZWRcXGxpYlxcYW1wLW1vZGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGlzSW5BbXBNb2RlKHtcbiAgYW1wRmlyc3QgPSBmYWxzZSxcbiAgaHlicmlkID0gZmFsc2UsXG4gIGhhc1F1ZXJ5ID0gZmFsc2UsXG59ID0ge30pOiBib29sZWFuIHtcbiAgcmV0dXJuIGFtcEZpcnN0IHx8IChoeWJyaWQgJiYgaGFzUXVlcnkpXG59XG4iXSwibmFtZXMiOlsiaXNJbkFtcE1vZGUiLCJhbXBGaXJzdCIsImh5YnJpZCIsImhhc1F1ZXJ5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    defaultHead: function() {\n        return defaultHead;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./side-effect */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            charSet: \"utf-8\"\n        }, \"charset\")\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }, \"viewport\"));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === 'string' || typeof child === 'number') {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === 'string' || typeof fragmentChild === 'number') {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    'name',\n    'httpEquiv',\n    'charSet',\n    'itemProp'\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf('$') + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case 'title':\n            case 'base':\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case 'meta':\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === 'charSet') {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (false) {}\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n                const srcMessage = c.props['src'] ? '<script> tag with src=\"' + c.props['src'] + '\"' : \"inline <script>\";\n                (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n            } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n                (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props['href'] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n        children: children\n    });\n}\n_c = Head;\nconst _default = Head;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\nvar _c;\n$RefreshReg$(_c, \"Head\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/side-effect.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return SideEffect;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst isServer = \"object\" === 'undefined';\nconst useClientOnlyLayoutEffect = isServer ? ()=>{} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? ()=>{} : _react.useEffect;\nfunction SideEffect(props) {\n    _s();\n    const { headManager, reduceComponentsToState } = props;\n    function emitChange() {\n        if (headManager && headManager.mountedInstances) {\n            const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n            headManager.updateHead(reduceComponentsToState(headElements, props));\n        }\n    }\n    if (isServer) {\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        emitChange();\n    }\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            var _headManager_mountedInstances;\n            headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    var _headManager_mountedInstances;\n                    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n    // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n    // being rendered, we only trigger the method from the last one.\n    // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n    // singleton in the layout effect pass, and actually trigger it in the effect pass.\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            if (headManager) {\n                headManager._pendingUpdate = emitChange;\n            }\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    if (headManager) {\n                        headManager._pendingUpdate = emitChange;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    useClientOnlyEffect({\n        \"SideEffect.useClientOnlyEffect\": ()=>{\n            if (headManager && headManager._pendingUpdate) {\n                headManager._pendingUpdate();\n                headManager._pendingUpdate = null;\n            }\n            return ({\n                \"SideEffect.useClientOnlyEffect\": ()=>{\n                    if (headManager && headManager._pendingUpdate) {\n                        headManager._pendingUpdate();\n                        headManager._pendingUpdate = null;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyEffect\"]);\n    return null;\n} //# sourceMappingURL=side-effect.js.map\n_s(SideEffect, \"gHVkikNHNxjVdD11eJBzaqkCiPY=\", false, function() {\n    return [\n        useClientOnlyLayoutEffect,\n        useClientOnlyLayoutEffect,\n        useClientOnlyEffect\n    ];\n});\n_c = SideEffect;\nvar _c;\n$RefreshReg$(_c, \"SideEffect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/warn-once.js ***!
  \**************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"warnOnce\", ({\n    enumerable: true,\n    get: function() {\n        return warnOnce;\n    }\n}));\nlet warnOnce = (_)=>{};\nif (true) {\n    const warnings = new Set();\n    warnOnce = (msg)=>{\n        if (!warnings.has(msg)) {\n            console.warn(msg);\n        }\n        warnings.add(msg);\n    };\n} //# sourceMappingURL=warn-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy93YXJuLW9uY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs0Q0FXU0E7OztlQUFBQTs7O0FBWFQsSUFBSUEsV0FBVyxDQUFDQyxLQUFlO0FBQy9CLElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDLE1BQU1HLFdBQVcsSUFBSUM7SUFDckJOLFdBQVcsQ0FBQ087UUFDVixJQUFJLENBQUNGLFNBQVNHLEdBQUcsQ0FBQ0QsTUFBTTtZQUN0QkUsUUFBUUMsSUFBSSxDQUFDSDtRQUNmO1FBQ0FGLFNBQVNNLEdBQUcsQ0FBQ0o7SUFDZjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hZ2FyYWp1XFxzcmNcXHNoYXJlZFxcbGliXFx1dGlsc1xcd2Fybi1vbmNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImxldCB3YXJuT25jZSA9IChfOiBzdHJpbmcpID0+IHt9XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBjb25zdCB3YXJuaW5ncyA9IG5ldyBTZXQ8c3RyaW5nPigpXG4gIHdhcm5PbmNlID0gKG1zZzogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCF3YXJuaW5ncy5oYXMobXNnKSkge1xuICAgICAgY29uc29sZS53YXJuKG1zZylcbiAgICB9XG4gICAgd2FybmluZ3MuYWRkKG1zZylcbiAgfVxufVxuXG5leHBvcnQgeyB3YXJuT25jZSB9XG4iXSwibmFtZXMiOlsid2Fybk9uY2UiLCJfIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwid2FybmluZ3MiLCJTZXQiLCJtc2ciLCJoYXMiLCJjb25zb2xlIiwid2FybiIsImFkZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["pages/_app","main"], () => (__webpack_exec__("(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);
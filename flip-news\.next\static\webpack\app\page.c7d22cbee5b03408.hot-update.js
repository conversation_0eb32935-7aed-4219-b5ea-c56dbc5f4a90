"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/news-grid.tsx":
/*!**********************************!*\
  !*** ./components/news-grid.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewsGrid: () => (/* binding */ NewsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _news_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./news-card */ \"(app-pages-browser)/./components/news-card.tsx\");\n/* harmony import */ var _ad_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ad-card */ \"(app-pages-browser)/./components/ad-card.tsx\");\n/* harmony import */ var _lib_seed_data__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/seed-data */ \"(app-pages-browser)/./lib/seed-data.ts\");\n/* __next_internal_client_entry_do_not_use__ NewsGrid auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NewsGrid() {\n    _s();\n    const [news, setNews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewsGrid.useEffect\": ()=>{\n            fetchNews();\n            fetchAds();\n        }\n    }[\"NewsGrid.useEffect\"], []);\n    const fetchNews = async ()=>{\n        try {\n            const response = await fetch(\"/api/public/news\");\n            if (response.ok) {\n                const data = await response.json();\n                setNews(data.news || []);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch news:\", error);\n            // For demo purposes, show Telugu sample data\n            setNews(_lib_seed_data__WEBPACK_IMPORTED_MODULE_4__.sampleTeluguNews);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAds = async ()=>{\n        try {\n            const response = await fetch(\"/api/public/ads\");\n            if (response.ok) {\n                const data = await response.json();\n                setAds(data.ads || []);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch ads:\", error);\n            // For demo purposes, show sample ads\n            setAds(sampleAds);\n        }\n    };\n    // Sample data for demo\n    const sampleNews = [\n        {\n            id: \"1\",\n            title: \"Breaking: Major Technology Breakthrough Announced\",\n            excerpt: \"Scientists have made a significant discovery that could revolutionize the tech industry...\",\n            image: \"https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop\",\n            category: \"Technology\",\n            createdAt: new Date().toISOString(),\n            views: 1250,\n            likes: 89,\n            shares: 23,\n            author: {\n                name: \"Tech Reporter\"\n            }\n        },\n        {\n            id: \"2\",\n            title: \"Sports Update: Championship Finals This Weekend\",\n            excerpt: \"The most anticipated match of the season is set to take place this weekend...\",\n            image: \"https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=400&h=300&fit=crop\",\n            category: \"Sports\",\n            createdAt: new Date(Date.now() - 3600000).toISOString(),\n            views: 890,\n            likes: 67,\n            shares: 15,\n            author: {\n                name: \"Sports Desk\"\n            }\n        },\n        {\n            id: \"3\",\n            title: \"Economic Markets Show Strong Growth\",\n            excerpt: \"Financial analysts report positive trends in the global markets this quarter...\",\n            image: \"https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400&h=300&fit=crop\",\n            category: \"Business\",\n            createdAt: new Date(Date.now() - 7200000).toISOString(),\n            views: 654,\n            likes: 45,\n            shares: 12,\n            author: {\n                name: \"Business Reporter\"\n            }\n        }\n    ];\n    const sampleAds = [\n        {\n            id: \"1\",\n            title: \"Special Offer - 50% Off\",\n            image: \"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=200&fit=crop\",\n            link: \"#\"\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n            children: [\n                ...Array(6)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-300 rounded-lg h-48 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-300 rounded mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-300 rounded w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    // Insert ads every 3 news items\n    const contentItems = [];\n    let adIndex = 0;\n    for(let i = 0; i < news.length; i++){\n        contentItems.push({\n            type: \"news\",\n            data: news[i]\n        });\n        // Insert ad every 3 items\n        if ((i + 1) % 3 === 0 && adIndex < ads.length) {\n            contentItems.push({\n                type: \"ad\",\n                data: ads[adIndex]\n            });\n            adIndex++;\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: contentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.type === \"news\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_news_card__WEBPACK_IMPORTED_MODULE_2__.NewsCard, {\n                    news: item.data\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ad_card__WEBPACK_IMPORTED_MODULE_3__.AdCard, {\n                    ad: item.data\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 13\n                }, this)\n            }, \"\".concat(item.type, \"-\").concat(index), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Voicebird news\\\\flip-news\\\\components\\\\news-grid.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsGrid, \"D7QK5GZWIMvlr+9isYSn2xu/8EA=\");\n_c = NewsGrid;\nvar _c;\n$RefreshReg$(_c, \"NewsGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/news-grid.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/seed-data.ts":
/*!**************************!*\
  !*** ./lib/seed-data.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   sampleTeluguNews: () => (/* binding */ sampleTeluguNews)\n/* harmony export */ });\nconst sampleTeluguNews = [\n    {\n        id: \"1\",\n        title: \"పెదలకు గూడు కట్టించడమే కాంగ్రెస్ లక్ష్యం\",\n        excerpt: \"హుజూరాబాద్ జిల్లా హుసన్ పల్లి మండలంలో వందలాది కుటుంబాలు ఇళ్లు కట్టించుకోవడానికి 200 మంది ఇంజినీర్లు ఇళ్లు మంజూరు పత్రాలు పంపిణీ చేశారు. ఈ ఇంద్రు పెదలకు ఆత్మగౌరవ ప్రతిష్ఠగా అభివృద్ధి చేయాలని. 5 లక్షల ఆర్థిక సహాయం నాలుగు దశల్లో లభించనుందని పేర్కొన్నారు. ఈ సందర్భంగా రైతులకు వివిధ్యాల కూడా పంపిణీ చేశారు.\",\n        image: \"https://images.unsplash.com/photo-1586339949916-3e9457bef6d3?w=800&h=600&fit=crop\",\n        category: \"Politics\",\n        createdAt: \"2024-01-15T10:30:00Z\",\n        views: 1250,\n        likes: 89,\n        shares: 23,\n        author: {\n            name: \"Adimulam Dileep\"\n        }\n    },\n    {\n        id: \"2\",\n        title: \"రైతులకు ఉచిత విద్యుత్ సరఫరా కొనసాగింపు\",\n        excerpt: \"తెలంగాణ రాష్ట్రంలో రైతులకు ఉచిత విద్యుత్ సరఫరా కొనసాగిస్తామని ముఖ్యమంత్రి రేవంత్ రెడ్డి హామీ ఇచ్చారు. వ్యవసాయ రంగ అభివృద్ధికి ప్రభుత్వం కట్టుబడి ఉందని తెలిపారు. రైతుల సమస్యలను పరిష్కరించడానికి ప్రత్యేక కమిటీలను ఏర్పాటు చేస్తామని వెల్లడించారు.\",\n        image: \"https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=800&h=600&fit=crop\",\n        category: \"Agriculture\",\n        createdAt: \"2024-01-15T08:15:00Z\",\n        views: 2100,\n        likes: 156,\n        shares: 45,\n        author: {\n            name: \"Srinivas Reddy\"\n        }\n    },\n    {\n        id: \"3\",\n        title: \"హైదరాబాద్‌లో మెట్రో రైలు సేవలు విస్తరణ\",\n        excerpt: \"హైదరాబాద్ మెట్రో రైలు సేవలను మరింత విస్తరించనున్నట్లు ప్రభుత్వం ప్రకటించింది. కొత్త రూట్లు మరియు స్టేషన్లను జోడించి ప్రజలకు మెరుగైన రవాణా సౌకర్యాలను అందించనున్నారు. దీనితో ట్రాఫిక్ సమస్యలు తగ్గుతాయని అధికారులు భావిస్తున్నారు.\",\n        image: \"https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=800&h=600&fit=crop\",\n        category: \"Transport\",\n        createdAt: \"2024-01-15T06:45:00Z\",\n        views: 1800,\n        likes: 134,\n        shares: 67,\n        author: {\n            name: \"Madhavi Latha\"\n        }\n    },\n    {\n        id: \"4\",\n        title: \"విద్యా రంగంలో కొత్త సంస్కరణలు\",\n        excerpt: \"తెలంగాణ రాష్ట్రంలో విద్యా రంగంలో కొత్త సంస్కరణలను ప్రవేశపెట్టనున్నట్లు విద్యా మంత్రి ప్రకటించారు. డిజిటల్ విద్య, ఉచిత పుస్తకాలు మరియు మిడ్ డే మీల్ పథకాలను మెరుగుపరచనున్నారు. ప్రభుత్వ పాఠశాలల్లో మౌలిక సదుపాయాలను అభివృద్ధి పరుస్తామని హామీ ఇచ్చారు.\",\n        image: \"https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=800&h=600&fit=crop\",\n        category: \"Education\",\n        createdAt: \"2024-01-14T16:20:00Z\",\n        views: 950,\n        likes: 78,\n        shares: 34,\n        author: {\n            name: \"Ramesh Kumar\"\n        }\n    },\n    {\n        id: \"5\",\n        title: \"ఆరోగ్య రంగంలో కొత్త పథకాలు\",\n        excerpt: \"రాష్ట్రంలో ఆరోగ్య రంగ అభివృద్ధికి కొత్త పథకాలను ప్రవేశపెట్టనున్నట్లు ఆరోగ్య మంత్రి తెలిపారు. ప్రాథమిక ఆరోగ్య కేంద్రాలను మెరుగుపరచడం, వైద్య సిబ్బంది నియామకాలు మరియు ఉచిత వైద్య సేవలను విస్తరించనున్నారు. గ్రామీణ ప్రాంతాల్లో మొబైల్ వైద్య యూనిట్లను ప్రారంభించనున్నారు.\",\n        image: \"https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop\",\n        category: \"Health\",\n        createdAt: \"2024-01-14T14:10:00Z\",\n        views: 1650,\n        likes: 112,\n        shares: 28,\n        author: {\n            name: \"Priya Sharma\"\n        }\n    },\n    {\n        id: \"6\",\n        title: \"క్రీడా రంగ అభివృద్ధికి కొత్త కార్యక్రమాలు\",\n        excerpt: \"తెలంగాణ రాష్ట్రంలో క్రీడా రంగ అభివృద్ధికి కొత్త కార్యక్రమాలను ప్రారంభించనున్నట్లు క్రీడా మంత్రి ప్రకటించారు. యువతకు క్రీడా సౌకర్యాలను అందించడం, కోచింగ్ సెంటర్లు స్థాపించడం మరియు క్రీడాకారులకు ప్రోత్సాహకాలు అందించనున్నారు. అంతర్జాతీయ స్థాయి క్రీడా సముదాయాలను నిర్మించనున్నారు.\",\n        image: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop\",\n        category: \"Sports\",\n        createdAt: \"2024-01-14T12:30:00Z\",\n        views: 1320,\n        likes: 95,\n        shares: 41,\n        author: {\n            name: \"Venkat Rao\"\n        }\n    }\n];\nconst categories = [\n    \"Politics\",\n    \"Agriculture\",\n    \"Transport\",\n    \"Education\",\n    \"Health\",\n    \"Sports\",\n    \"Technology\",\n    \"Entertainment\",\n    \"Business\",\n    \"Culture\"\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/seed-data.ts\n"));

/***/ })

});